import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Typography,
  IconButton,
  Badge
} from '@mui/material';
import {
  Notifications,
  Close,
  CheckCircle,
  Info,
  Warning
} from '@mui/icons-material';

const NotificationSystem = () => {
  const [notifications, setNotifications] = useState([]);
  const [open, setOpen] = useState(false);
  const [currentNotification, setCurrentNotification] = useState(null);

  useEffect(() => {
    // Simulate incoming notifications
    const interval = setInterval(() => {
      const randomNotifications = [
        {
          id: Date.now(),
          type: 'success',
          title: 'Weather Updated!',
          message: 'Current weather conditions have been refreshed.',
          icon: <CheckCircle />
        },
        {
          id: Date.now() + 1,
          type: 'info',
          title: 'New Activity Suggestion',
          message: 'Based on your mood, we recommend visiting the Library.',
          icon: <Info />
        },
        {
          id: Date.now() + 2,
          type: 'warning',
          title: 'Low Energy Detected',
          message: 'Consider taking a break or visiting Sandy Dunes for relaxation.',
          icon: <Warning />
        }
      ];

      const randomNotification = randomNotifications[Math.floor(Math.random() * randomNotifications.length)];
      
      if (Math.random() < 0.3) { // 30% chance of notification
        addNotification(randomNotification);
      }
    }, 10000); // Check every 10 seconds

    return () => clearInterval(interval);
  }, []);

  const addNotification = (notification) => {
    setNotifications(prev => [notification, ...prev.slice(0, 4)]); // Keep only 5 notifications
    setCurrentNotification(notification);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleRemoveNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const getSeverity = (type) => {
    switch (type) {
      case 'success': return 'success';
      case 'info': return 'info';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'info';
    }
  };

  return (
    <Box>
      {/* Notification Badge */}
      <Badge badgeContent={notifications.length} color="error">
        <IconButton color="inherit">
          <Notifications />
        </IconButton>
      </Badge>

      {/* Current Notification Snackbar */}
      <Snackbar
        open={open}
        autoHideDuration={4000}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        {currentNotification && (
          <Alert
            onClose={handleClose}
            severity={getSeverity(currentNotification.type)}
            sx={{ width: '100%' }}
            icon={currentNotification.icon}
          >
            <Box>
              <Typography variant="subtitle2" fontWeight="bold">
                {currentNotification.title}
              </Typography>
              <Typography variant="body2">
                {currentNotification.message}
              </Typography>
            </Box>
          </Alert>
        )}
      </Snackbar>

      {/* Notification History (if needed) */}
      {notifications.length > 0 && (
        <Box
          sx={{
            position: 'fixed',
            top: 80,
            right: 20,
            zIndex: 1000,
            maxWidth: 300,
            maxHeight: 400,
            overflow: 'auto',
            backgroundColor: 'background.paper',
            borderRadius: 2,
            boxShadow: 3,
            p: 1
          }}
        >
          {notifications.map((notification) => (
            <Alert
              key={notification.id}
              severity={getSeverity(notification.type)}
              sx={{ mb: 1 }}
              action={
                <IconButton
                  size="small"
                  onClick={() => handleRemoveNotification(notification.id)}
                >
                  <Close fontSize="small" />
                </IconButton>
              }
            >
              <Box>
                <Typography variant="caption" fontWeight="bold">
                  {notification.title}
                </Typography>
                <Typography variant="caption" display="block">
                  {notification.message}
                </Typography>
              </Box>
            </Alert>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default NotificationSystem; 