import React, { useRef, useEffect, useState } from 'react';
import { Box, Button, Typography, CircularProgress } from '@mui/material';
import { CameraAlt, Stop } from '@mui/icons-material';
import * as faceapi from 'face-api.js';

const FaceRecognition = ({ onEmotionDetected }) => {
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        faceapi.nets.tinyFaceDetector.loadFromUri('/models'),
        faceapi.nets.faceExpressionNet.loadFromUri('/models')
      ]);
      console.log('Face detection models loaded');
    } catch (error) {
      console.error('Error loading models:', error);
      setError('Failed to load face detection models');
    } finally {
      setIsLoading(false);
    }
  };

  const startVideo = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      videoRef.current.srcObject = stream;
      setIsStreaming(true);
      setError('');
    } catch (error) {
      console.error('Error accessing camera:', error);
      setError('Unable to access camera. Please check permissions.');
    }
  };

  const stopVideo = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject;
      const tracks = stream.getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setIsStreaming(false);
  };

  const detectEmotion = async () => {
    if (!videoRef.current || !canvasRef.current) return;

    try {
      const detections = await faceapi
        .detectAllFaces(videoRef.current, new faceapi.TinyFaceDetectorOptions())
        .withFaceExpressions();

      if (detections.length > 0) {
        const expressions = detections[0].expressions;
        const emotion = getDominantEmotion(expressions);
        onEmotionDetected(emotion);
      } else {
        setError('No face detected. Please position your face in the camera.');
      }
    } catch (error) {
      console.error('Error detecting emotion:', error);
      setError('Error detecting emotion. Please try again.');
    }
  };

  const getDominantEmotion = (expressions) => {
    const emotions = Object.entries(expressions);
    const dominantEmotion = emotions.reduce((a, b) => a[1] > b[1] ? a : b);
    
    // Map face-api emotions to our emotion categories
    const emotionMap = {
      happy: 'happy',
      sad: 'sad',
      angry: 'tired',
      disgusted: 'tired',
      fearful: 'tired',
      surprised: 'excited',
      neutral: 'relaxed'
    };

    return emotionMap[dominantEmotion[0]] || 'relaxed';
  };

  const handleVideoLoad = () => {
    if (videoRef.current) {
      videoRef.current.play();
    }
  };

  useEffect(() => {
    return () => {
      stopVideo();
    };
  }, []);

  if (isLoading) {
    return (
      <Box display="flex" flexDirection="column" alignItems="center" p={3}>
        <CircularProgress />
        <Typography variant="body2" sx={{ mt: 2 }}>
          Loading face detection models...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Box className="camera-container">
        <video
          ref={videoRef}
          onLoadedMetadata={handleVideoLoad}
          style={{
            width: '100%',
            height: 'auto',
            borderRadius: '12px'
          }}
          muted
        />
        <canvas
          ref={canvasRef}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%'
          }}
        />
      </Box>

      <Box display="flex" justifyContent="center" gap={2} mt={2}>
        {!isStreaming ? (
          <Button
            variant="contained"
            startIcon={<CameraAlt />}
            onClick={startVideo}
            disabled={isLoading}
          >
            Start Camera
          </Button>
        ) : (
          <>
            <Button
              variant="contained"
              color="primary"
              onClick={detectEmotion}
              disabled={!isStreaming}
            >
              Detect Emotion
            </Button>
            <Button
              variant="outlined"
              startIcon={<Stop />}
              onClick={stopVideo}
            >
              Stop Camera
            </Button>
          </>
        )}
      </Box>

      {error && (
        <Typography color="error" variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
          {error}
        </Typography>
      )}

      <Typography variant="body2" color="text.secondary" sx={{ mt: 2, textAlign: 'center' }}>
        Position your face in the camera and click "Detect Emotion" to get a recommendation
      </Typography>
    </Box>
  );
};

export default FaceRecognition; 