const { spawn } = require('child_process');
const portfinder = require('portfinder');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

console.log(`${colors.cyan}${colors.bright}🚀 Campus Seating App - Smart Port Finder${colors.reset}\n`);

async function findAvailablePorts() {
  try {
    // Find available server port (5000-5010)
    const serverPort = await portfinder.getPortPromise({
      port: 5000,
      stopPort: 5010
    });
    
    // Find available client port (3001-3010)
    const clientPort = await portfinder.getPortPromise({
      port: 3001,
      stopPort: 3010
    });
    
    console.log(`${colors.green}✅ Found available ports:${colors.reset}`);
    console.log(`   Server: ${colors.cyan}${serverPort}${colors.reset}`);
    console.log(`   Client: ${colors.cyan}${clientPort}${colors.reset}\n`);
    
    return { serverPort, clientPort };
  } catch (error) {
    console.error(`${colors.red}❌ Failed to find available ports:${colors.reset}`, error.message);
    process.exit(1);
  }
}

async function startServer(port) {
  return new Promise((resolve, reject) => {
    console.log(`${colors.yellow}🔄 Starting server on port ${port}...${colors.reset}`);
    
    const server = spawn('npm', ['run', 'dev'], {
      cwd: path.join(__dirname, 'server'),
      stdio: 'pipe',
      shell: true,
      env: { ...process.env, PORT: port.toString() }
    });
    
    server.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Server running on port')) {
        console.log(`${colors.green}✅ Server started successfully on port ${port}${colors.reset}`);
        resolve(server);
      }
      process.stdout.write(`${colors.blue}[SERVER]${colors.reset} ${output}`);
    });
    
    server.stderr.on('data', (data) => {
      process.stderr.write(`${colors.red}[SERVER ERROR]${colors.reset} ${data}`);
    });
    
    server.on('error', (error) => {
      console.error(`${colors.red}❌ Failed to start server:${colors.reset}`, error.message);
      reject(error);
    });
    
    server.on('close', (code) => {
      if (code !== 0) {
        console.error(`${colors.red}❌ Server process exited with code ${code}${colors.reset}`);
      }
    });
  });
}

async function startClient(port) {
  return new Promise((resolve, reject) => {
    console.log(`${colors.yellow}🔄 Starting client on port ${port}...${colors.reset}`);
    
    const client = spawn('npm', ['start'], {
      cwd: path.join(__dirname, 'client'),
      stdio: 'pipe',
      shell: true,
      env: { ...process.env, PORT: port.toString() }
    });
    
    client.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Local:') || output.includes('On Your Network:')) {
        console.log(`${colors.green}✅ Client started successfully on port ${port}${colors.reset}`);
        resolve(client);
      }
      process.stdout.write(`${colors.magenta}[CLIENT]${colors.reset} ${output}`);
    });
    
    client.stderr.on('data', (data) => {
      process.stderr.write(`${colors.red}[CLIENT ERROR]${colors.reset} ${data}`);
    });
    
    client.on('error', (error) => {
      console.error(`${colors.red}❌ Failed to start client:${colors.reset}`, error.message);
      reject(error);
    });
    
    client.on('close', (code) => {
      if (code !== 0) {
        console.error(`${colors.red}❌ Client process exited with code ${code}${colors.reset}`);
      }
    });
  });
}

async function main() {
  try {
    // Find available ports
    const { serverPort, clientPort } = await findAvailablePorts();
    
    // Start server and client
    const server = await startServer(serverPort);
    const client = await startClient(clientPort);
    
    console.log(`\n${colors.green}${colors.bright}🎉 Both servers started successfully!${colors.reset}`);
    console.log(`\n${colors.cyan}📱 Access your app at:${colors.reset}`);
    console.log(`   Frontend: ${colors.bright}http://localhost:${clientPort}${colors.reset}`);
    console.log(`   Backend:  ${colors.bright}http://localhost:${serverPort}${colors.reset}`);
    console.log(`   Health:   ${colors.bright}http://localhost:${serverPort}/api/health${colors.reset}`);
    
    console.log(`\n${colors.yellow}💡 Press Ctrl+C to stop both servers${colors.reset}\n`);
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log(`\n${colors.yellow}🛑 Shutting down servers...${colors.reset}`);
      server.kill('SIGINT');
      client.kill('SIGINT');
      process.exit(0);
    });
    
    process.on('SIGTERM', () => {
      console.log(`\n${colors.yellow}🛑 Shutting down servers...${colors.reset}`);
      server.kill('SIGTERM');
      client.kill('SIGTERM');
      process.exit(0);
    });
    
  } catch (error) {
    console.error(`${colors.red}❌ Failed to start application:${colors.reset}`, error.message);
    process.exit(1);
  }
}

// Run the main function
main().catch(console.error); 