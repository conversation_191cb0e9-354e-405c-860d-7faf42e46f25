const express = require('express');
const axios = require('axios');
const { verifyToken } = require('../middleware/auth');

const router = express.Router();

// Get current weather
router.get('/current', verifyToken, async (req, res) => {
  try {
    const { lat = 12.9716, lon = 77.5946 } = req.query; // Default to Bangalore coordinates
    const apiKey = process.env.OPENWEATHER_API_KEY;
    
    if (!apiKey) {
      console.warn('OpenWeather API key not found, using mock data');
      return res.json(getMockWeatherData());
    }

    const response = await axios.get(
      `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${apiKey}&units=metric`
    );

    const weatherData = {
      temperature: response.data.main.temp,
      feels_like: response.data.main.feels_like,
      humidity: response.data.main.humidity,
      description: response.data.weather[0].description,
      main: response.data.weather[0].main,
      wind_speed: response.data.wind.speed,
      is_rainy: response.data.weather[0].main === 'Rain',
      is_sunny: response.data.weather[0].main === 'Clear',
      is_cloudy: response.data.weather[0].main === 'Clouds'
    };

    res.json(weatherData);
  } catch (error) {
    console.error('Weather API error:', error.message);
    // Return mock data if API fails
    res.json(getMockWeatherData());
  }
});

// Get weather forecast
router.get('/forecast', verifyToken, async (req, res) => {
  try {
    const { lat = 12.9716, lon = 77.5946 } = req.query; // Default to Bangalore coordinates
    const apiKey = process.env.OPENWEATHER_API_KEY;
    
    if (!apiKey) {
      console.warn('OpenWeather API key not found, using mock forecast data');
      return res.json(getMockForecastData());
    }

    const response = await axios.get(
      `https://api.openweathermap.org/data/2.5/forecast?lat=${lat}&lon=${lon}&appid=${apiKey}&units=metric`
    );

    const forecastData = response.data.list.map(item => ({
      datetime: item.dt_txt,
      temperature: item.main.temp,
      feels_like: item.main.feels_like,
      humidity: item.main.humidity,
      description: item.weather[0].description,
      main: item.weather[0].main,
      wind_speed: item.wind.speed,
      is_rainy: item.weather[0].main === 'Rain',
      is_sunny: item.weather[0].main === 'Clear',
      is_cloudy: item.weather[0].main === 'Clouds'
    }));

    res.json(forecastData);
  } catch (error) {
    console.error('Weather forecast API error:', error.message);
    // Return mock data if API fails
    res.json(getMockForecastData());
  }
});

// Helper function to generate mock weather data
function getMockWeatherData() {
  const conditions = [
    { main: 'Clear', description: 'clear sky', is_sunny: true, is_rainy: false, is_cloudy: false },
    { main: 'Clouds', description: 'partly cloudy', is_sunny: false, is_rainy: false, is_cloudy: true },
    { main: 'Rain', description: 'light rain', is_sunny: false, is_rainy: true, is_cloudy: false }
  ];
  
  const condition = conditions[Math.floor(Math.random() * conditions.length)];
  const baseTemp = 20 + Math.random() * 15; // 20-35°C
  
  return {
    temperature: Math.round(baseTemp * 10) / 10,
    feels_like: Math.round((baseTemp + (Math.random() - 0.5) * 4) * 10) / 10,
    humidity: Math.round(40 + Math.random() * 40), // 40-80%
    description: condition.description,
    main: condition.main,
    wind_speed: Math.round((Math.random() * 10) * 10) / 10, // 0-10 m/s
    is_rainy: condition.is_rainy,
    is_sunny: condition.is_sunny,
    is_cloudy: condition.is_cloudy
  };
}

// Helper function to generate mock forecast data
function getMockForecastData() {
  const forecast = [];
  const now = new Date();
  
  for (let i = 0; i < 40; i++) { // 5 days * 8 times per day (3-hour intervals)
    const date = new Date(now.getTime() + i * 3 * 60 * 60 * 1000); // 3 hours apart
    const mockData = getMockWeatherData();
    
    forecast.push({
      datetime: date.toISOString().replace('T', ' ').substring(0, 19),
      ...mockData
    });
  }
  
  return forecast;
}

module.exports = router;
