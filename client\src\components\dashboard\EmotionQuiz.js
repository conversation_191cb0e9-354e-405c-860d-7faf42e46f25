import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Stepper,
  Step,
  StepLabel,
  Chip,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Psychology,
  TrendingUp,
  CheckCircle
} from '@mui/icons-material';

const EmotionQuiz = ({ onEmotionResult }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [answers, setAnswers] = useState({});
  const [showResult, setShowResult] = useState(false);
  const [quizResult, setQuizResult] = useState(null);

  const questions = [
    {
      id: 'energy',
      question: 'How would you rate your energy level right now?',
      options: [
        { value: 'high', label: 'High Energy - Ready to take on the world!', emoji: '⚡' },
        { value: 'medium', label: 'Moderate - Feeling balanced', emoji: '😊' },
        { value: 'low', label: 'Low Energy - Need some rest', emoji: '😴' }
      ]
    },
    {
      id: 'mood',
      question: 'What best describes your current mood?',
      options: [
        { value: 'happy', label: 'Happy and Excited', emoji: '😄' },
        { value: 'focused', label: 'Focused and Determined', emoji: '🤔' },
        { value: 'relaxed', label: 'Relaxed and Calm', emoji: '😌' },
        { value: 'tired', label: 'Tired or Stressed', emoji: '😔' }
      ]
    },
    {
      id: 'activity',
      question: 'What type of activity are you looking for?',
      options: [
        { value: 'social', label: 'Social Interaction', emoji: '👥' },
        { value: 'physical', label: 'Physical Activity', emoji: '🏃' },
        { value: 'mental', label: 'Mental Work/Study', emoji: '📚' },
        { value: 'relaxation', label: 'Relaxation/Quiet Time', emoji: '🧘' }
      ]
    },
    {
      id: 'environment',
      question: 'What environment do you prefer right now?',
      options: [
        { value: 'indoor', label: 'Indoor - Comfortable and controlled', emoji: '🏠' },
        { value: 'outdoor', label: 'Outdoor - Fresh air and nature', emoji: '🌳' },
        { value: 'flexible', label: 'Either - I\'m adaptable', emoji: '🔄' }
      ]
    }
  ];

  const handleAnswerChange = (questionId, value) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  const handleNext = () => {
    if (activeStep < questions.length - 1) {
      setActiveStep(prev => prev + 1);
    } else {
      calculateResult();
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const calculateResult = () => {
    const { energy, mood, activity } = answers;
    
    let emotion = 'relaxed';
    let confidence = 0.8;

    // Determine emotion based on answers
    if (energy === 'high' && mood === 'happy') {
      emotion = 'excited';
      confidence = 0.9;
    } else if (energy === 'high' && activity === 'physical') {
      emotion = 'energetic';
      confidence = 0.85;
    } else if (mood === 'focused' && activity === 'mental') {
      emotion = 'focused';
      confidence = 0.9;
    } else if (energy === 'low' || mood === 'tired') {
      emotion = 'tired';
      confidence = 0.8;
    } else if (mood === 'relaxed' && activity === 'relaxation') {
      emotion = 'relaxed';
      confidence = 0.85;
    }

    const result = {
      emotion,
      confidence,
      answers,
      reasoning: getReasoning(answers, emotion)
    };

    setQuizResult(result);
    setShowResult(true);
    
    if (onEmotionResult) {
      onEmotionResult(emotion);
    }
  };

  const getReasoning = (answers, emotion) => {
    const reasoning = {
      excited: `Based on your high energy level and happy mood, you're clearly excited and ready for social activities.`,
      energetic: `Your high energy and preference for physical activity suggest you're feeling energetic and motivated.`,
      focused: `Your focused mood and preference for mental work indicate you're in a concentrated state.`,
      tired: `Your low energy or tired mood suggests you need a quiet, comfortable environment.`,
      relaxed: `Your relaxed mood and preference for quiet time show you're in a calm state.`
    };

    return reasoning[emotion] || 'Based on your responses, we\'ve determined your current emotional state.';
  };

  const getEmotionIcon = (emotion) => {
    const icons = {
      excited: '🎉',
      energetic: '⚡',
      focused: '🎯',
      tired: '😴',
      relaxed: '😌'
    };
    return icons[emotion] || '😊';
  };

  const progress = ((activeStep + 1) / questions.length) * 100;

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        <Psychology sx={{ mr: 1, verticalAlign: 'middle' }} />
        Emotion Assessment Quiz
      </Typography>

      <Card elevation={3}>
        <CardContent sx={{ p: 3 }}>
          <Box mb={3}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="body2" color="text.secondary">
                Progress: {activeStep + 1} of {questions.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {Math.round(progress)}%
              </Typography>
            </Box>
            <LinearProgress variant="determinate" value={progress} sx={{ height: 8, borderRadius: 4 }} />
          </Box>

          <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4 }}>
            {questions.map((question, index) => (
              <Step key={index}>
                <StepLabel>{question.id.charAt(0).toUpperCase() + question.id.slice(1)}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {activeStep < questions.length && (
            <Box>
              <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
                {questions[activeStep].question}
              </Typography>

              <FormControl component="fieldset" sx={{ width: '100%' }}>
                <RadioGroup
                  value={answers[questions[activeStep].id] || ''}
                  onChange={(e) => handleAnswerChange(questions[activeStep].id, e.target.value)}
                >
                  {questions[activeStep].options.map((option) => (
                    <FormControlLabel
                      key={option.value}
                      value={option.value}
                      control={<Radio />}
                      label={
                        <Box display="flex" alignItems="center">
                          <Typography variant="h5" sx={{ mr: 1 }}>
                            {option.emoji}
                          </Typography>
                          <Typography>{option.label}</Typography>
                        </Box>
                      }
                      sx={{
                        margin: 1,
                        padding: 2,
                        border: '1px solid #e0e0e0',
                        borderRadius: 2,
                        width: '100%',
                        '&:hover': {
                          backgroundColor: '#f5f5f5',
                          borderColor: 'primary.main'
                        },
                        '&.Mui-checked': {
                          backgroundColor: 'primary.light',
                          borderColor: 'primary.main'
                        }
                      }}
                    />
                  ))}
                </RadioGroup>
              </FormControl>

              <Box display="flex" justifyContent="space-between" mt={4}>
                <Button
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  variant="outlined"
                >
                  Back
                </Button>
                <Button
                  variant="contained"
                  onClick={handleNext}
                  disabled={!answers[questions[activeStep].id]}
                  endIcon={activeStep === questions.length - 1 ? <CheckCircle /> : null}
                >
                  {activeStep === questions.length - 1 ? 'Get Results' : 'Next'}
                </Button>
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Results Dialog */}
      <Dialog
        open={showResult}
        onClose={() => setShowResult(false)}
        maxWidth="sm"
        fullWidth
      >
        {quizResult && (
          <>
            <DialogTitle sx={{ textAlign: 'center', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
              <Box display="flex" alignItems="center" justifyContent="center">
                <Typography variant="h3" sx={{ mr: 2 }}>
                  {getEmotionIcon(quizResult.emotion)}
                </Typography>
                <Typography variant="h5">
                  Quiz Results
                </Typography>
              </Box>
            </DialogTitle>
            <DialogContent sx={{ pt: 3 }}>
              <Box textAlign="center" mb={3}>
                <Typography variant="h4" gutterBottom sx={{ color: 'primary.main' }}>
                  {quizResult.emotion.charAt(0).toUpperCase() + quizResult.emotion.slice(1)}
                </Typography>
                <Chip
                  label={`${Math.round(quizResult.confidence * 100)}% Confidence`}
                  color="success"
                  icon={<TrendingUp />}
                />
              </Box>

              <Typography variant="body1" paragraph>
                {quizResult.reasoning}
              </Typography>

              <Box mt={3}>
                <Typography variant="h6" gutterBottom>
                  Your Responses:
                </Typography>
                {Object.entries(quizResult.answers).map(([key, value]) => (
                  <Chip
                    key={key}
                    label={`${key}: ${value}`}
                    variant="outlined"
                    sx={{ m: 0.5 }}
                  />
                ))}
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setShowResult(false)}>Close</Button>
              <Button
                variant="contained"
                onClick={() => {
                  setShowResult(false);
                  setActiveStep(0);
                  setAnswers({});
                }}
              >
                Take Quiz Again
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default EmotionQuiz; 