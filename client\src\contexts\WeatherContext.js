import React, { createContext, useContext, useState } from 'react';
import axios from 'axios';
import { useAuth } from './AuthContext';

const WeatherContext = createContext();

export const useWeather = () => {
  const context = useContext(WeatherContext);
  if (!context) {
    throw new Error('useWeather must be used within a WeatherProvider');
  }
  return context;
};

export const WeatherProvider = ({ children }) => {
  const [weather, setWeather] = useState(null);
  const [loading, setLoading] = useState(true);
  const { token } = useAuth();

  const fetchWeather = async (lat = 12.9716, lon = 77.5946) => {
    if (!token) {
      setLoading(false);
      return;
    }
    
    setLoading(true);
    try {
      const response = await axios.get(`/api/weather/current?lat=${lat}&lon=${lon}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setWeather(response.data);
    } catch (error) {
      console.error('Error fetching weather:', error);
      // Set default weather data if API fails
      setWeather({
        temperature: 25,
        feels_like: 25,
        humidity: 60,
        description: 'Partly cloudy',
        main: 'Clouds',
        wind_speed: 5,
        is_rainy: false,
        is_sunny: false,
        is_cloudy: true
      });
    } finally {
      setLoading(false);
    }
  };

  const getWeatherIcon = (weatherMain) => {
    switch (weatherMain) {
      case 'Clear':
        return '☀️';
      case 'Clouds':
        return '☁️';
      case 'Rain':
        return '🌧️';
      case 'Snow':
        return '❄️';
      case 'Thunderstorm':
        return '⛈️';
      case 'Drizzle':
        return '🌦️';
      case 'Mist':
      case 'Fog':
        return '🌫️';
      default:
        return '🌤️';
    }
  };

  const getWeatherDescription = (weather) => {
    if (!weather) return 'Weather data unavailable';
    
    const temp = Math.round(weather.temperature);
    const icon = getWeatherIcon(weather.main);
    
    return `${icon} ${temp}°C - ${weather.description}`;
  };

  const isIndoorWeather = (weather) => {
    if (!weather) return false;
    return weather.is_rainy || weather.temperature < 15 || weather.temperature > 35;
  };

  const value = {
    weather,
    loading,
    fetchWeather,
    getWeatherIcon,
    getWeatherDescription,
    isIndoorWeather
  };

  return (
    <WeatherContext.Provider value={value}>
      {children}
    </WeatherContext.Provider>
  );
}; 