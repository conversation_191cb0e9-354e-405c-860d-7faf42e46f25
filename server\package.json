{"name": "campus-seating-server", "version": "1.0.0", "description": "Backend server for Campus Seating Allocation App", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "portfinder": "^1.0.37"}, "devDependencies": {"kill-port": "^2.0.1", "nodemon": "^3.0.2"}}