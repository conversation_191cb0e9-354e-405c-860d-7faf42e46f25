# PowerShell script to download face-api.js models
Write-Host "Downloading face-api.js models..." -ForegroundColor Green

# Create models directory
New-Item -ItemType Directory -Force -Path "client\public\models"

# Download models from face-api.js CDN
$modelsPath = "client\public\models"

# Download TinyFaceDetector model
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/tiny_face_detector_model-weights_manifest.json" -OutFile "$modelsPath\tiny_face_detector_model-weights_manifest.json"
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/tiny_face_detector_model-shard1" -OutFile "$modelsPath\tiny_face_detector_model-shard1"

# Download FaceExpressionNet model
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/face_expression_model-weights_manifest.json" -OutFile "$modelsPath\face_expression_model-weights_manifest.json"
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/face_expression_model-shard1" -OutFile "$modelsPath\face_expression_model-shard1"

Write-Host "Models downloaded successfully!" -ForegroundColor Green
Write-Host "You can now run the application with: npm run dev" -ForegroundColor Yellow 