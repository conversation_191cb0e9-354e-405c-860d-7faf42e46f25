import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  CircularProgress
} from '@mui/material';
import {
  LocationOn,
  Psychology,
  Lightbulb
} from '@mui/icons-material';

const RecommendationDisplay = ({ recommendation, loading }) => {
  if (loading) {
    return (
      <Card className="recommendation-card">
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <CircularProgress sx={{ color: 'white', mb: 2 }} />
          <Typography variant="h6" sx={{ color: 'white' }}>
            Analyzing your mood...
          </Typography>
        </CardContent>
      </Card>
    );
  }

  if (!recommendation) {
    return null;
  }

  const getLocationIcon = (location) => {
    switch (location) {
      case 'Peacock Circle':
        return '🎭';
      case 'Second Turf':
        return '⚽';
      case 'Library':
        return '📚';
      case 'Sandy Dunes':
        return '🏖️';
      default:
        return '📍';
    }
  };



  return (
    <Card className="recommendation-card">
      <CardContent>
        <Box display="flex" alignItems="center" mb={3}>
          <Lightbulb sx={{ fontSize: 40, mr: 2 }} />
          <Typography variant="h4" sx={{ color: 'white' }}>
            Your Recommendation
          </Typography>
        </Box>

        <Box display="flex" alignItems="center" justifyContent="center" mb={3}>
          <Typography variant="h3" sx={{ color: 'white', mr: 2 }}>
            {getLocationIcon(recommendation.recommendedLocation)}
          </Typography>
          <Typography variant="h3" sx={{ color: 'white', fontWeight: 'bold' }}>
            {recommendation.recommendedLocation}
          </Typography>
        </Box>

        <Box display="flex" flexWrap="wrap" gap={2} justifyContent="center" mb={3}>
          <Chip
            icon={<Psychology />}
            label={`Mood: ${recommendation.emotion}`}
            sx={{ 
              backgroundColor: 'rgba(255,255,255,0.2)', 
              color: 'white',
              fontSize: '1.1rem',
              padding: '8px'
            }}
          />

          <Chip
            icon={<LocationOn />}
            label={`Location Type: ${recommendation.recommendedLocation === 'Library' || recommendation.recommendedLocation === 'Peacock Circle' ? 'Indoor' : 'Outdoor'}`}
            sx={{ 
              backgroundColor: 'rgba(255,255,255,0.2)', 
              color: 'white',
              fontSize: '1.1rem',
              padding: '8px'
            }}
          />
        </Box>

        <Box 
          sx={{ 
            backgroundColor: 'rgba(255,255,255,0.1)', 
            p: 3, 
            borderRadius: 2,
            border: '1px solid rgba(255,255,255,0.2)'
          }}
        >
          <Typography variant="h6" sx={{ color: 'white', mb: 2 }}>
            Why this location?
          </Typography>
          <Typography variant="body1" sx={{ color: 'white', lineHeight: 1.6 }}>
            {recommendation.reasoning}
          </Typography>
        </Box>

        <Box mt={3} textAlign="center">
          <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
            💡 This recommendation is based on your current mood and preferences
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default RecommendationDisplay; 