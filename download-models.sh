#!/bin/bash

# Script to download face-api.js models
echo "Downloading face-api.js models..."

# Create models directory
mkdir -p client/public/models

# Download models from face-api.js CDN
cd client/public/models

# Download TinyFaceDetector model
curl -O https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/tiny_face_detector_model-weights_manifest.json
curl -O https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/tiny_face_detector_model-shard1

# Download FaceExpressionNet model
curl -O https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/face_expression_model-weights_manifest.json
curl -O https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/face_expression_model-shard1

echo "Models downloaded successfully!"
echo "You can now run the application with: npm run dev" 