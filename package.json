{"name": "campus-seating-app", "version": "1.0.0", "description": "A Campus Seating Allocation App with face recognition and weather integration", "main": "server/index.js", "scripts": {"start": "node start.js", "dev": "concurrently \"npm run server\" \"npm run client\"", "dev:cross": "concurrently --kill-others \"npm run server\" \"npm run client:auto\"", "dev:smart": "node start.js", "server": "cd server && npm run dev", "client": "cd client && npm start", "client:auto": "cd client && npm run start:auto", "client:fallback": "cd client && npm run start:fallback", "build": "cd client && npm run build", "install-all": "npm install && cd server && npm install && cd ../client && npm install", "kill-ports": "npx kill-port 3000 3001 5000 5001 5002 5003 5004 5005 5006 5007 5008 5009 5010"}, "keywords": ["campus", "seating", "face-recognition", "weather", "react", "nodejs"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "kill-port": "^2.0.1"}, "dependencies": {"portfinder": "^1.0.37"}}