import React from 'react';
import { Box, Typography, CircularProgress } from '@mui/material';
import { WbSunny, Opacity, Air } from '@mui/icons-material';
import { useWeather } from '../../contexts/WeatherContext';

const WeatherDisplay = () => {
  const { weather, loading, getWeatherDescription } = useWeather();

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={3}>
        <CircularProgress />
      </Box>
    );
  }

  if (!weather) {
    return (
      <Box textAlign="center" p={3}>
        <Typography variant="body1" color="text.secondary">
          Weather data unavailable
        </Typography>
      </Box>
    );
  }

  const safeRound = (value) => {
    return typeof value === 'number' && isFinite(value) ? Math.round(value) : '—';
  };

  const safeFixed = (value, digits = 1) => {
    return typeof value === 'number' && isFinite(value) ? value.toFixed(digits) : '—';
  };

  return (
    <Box className="weather-card" sx={{ color: '#e6f0ff' }}>
      <Typography variant="h4" gutterBottom sx={{ color: '#e6f0ff' }}>
        {getWeatherDescription(weather)}
      </Typography>
      
      <Box display="flex" justifyContent="space-around" mt={2}>
        <Box textAlign="center">
          <Opacity sx={{ fontSize: 40, mb: 1, color: '#e6f0ff' }} />
          <Typography variant="h6" sx={{ color: '#e6f0ff' }}>{safeRound(weather.humidity)}%</Typography>
          <Typography variant="body2" sx={{ color: 'rgba(230,240,255,0.75)' }}>Humidity</Typography>
        </Box>
        
        <Box textAlign="center">
          <Air sx={{ fontSize: 40, mb: 1, color: '#e6f0ff' }} />
          <Typography variant="h6" sx={{ color: '#e6f0ff' }}>{safeFixed(weather.wind_speed)} m/s</Typography>
          <Typography variant="body2" sx={{ color: 'rgba(230,240,255,0.75)' }}>Wind Speed</Typography>
        </Box>
        
        <Box textAlign="center">
          <WbSunny sx={{ fontSize: 40, mb: 1, color: '#e6f0ff' }} />
          <Typography variant="h6" sx={{ color: '#e6f0ff' }}>{safeRound(weather.feels_like)}°C</Typography>
          <Typography variant="body2" sx={{ color: 'rgba(230,240,255,0.75)' }}>Feels Like</Typography>
        </Box>
      </Box>

      <Box mt={2} p={2} sx={{ backgroundColor: 'rgba(255,255,255,0.08)', borderRadius: 2, border: '1px solid rgba(255,255,255,0.12)' }}>
        <Typography variant="body2" sx={{ color: 'rgba(230,240,255,0.9)' }}>
          {weather.is_rainy && "🌧️ It's raining - indoor activities recommended"}
          {!weather.is_rainy && weather.is_sunny && '☀️ Sunny weather - great for outdoor activities'}
          {!weather.is_rainy && !weather.is_sunny && weather.is_cloudy && '☁️ Cloudy weather - flexible indoor/outdoor options'}
        </Typography>
      </Box>
    </Box>
  );
};

export default WeatherDisplay; 