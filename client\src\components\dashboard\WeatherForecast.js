import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  IconButton,
  Collapse,
  LinearProgress,
  Tabs,
  Tab
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess,
  WbSunny,
  Cloud,
  Opacity,
  Air,
  Visibility,
  Thermostat,
  Schedule
} from '@mui/icons-material';
import { useWeather } from '../../contexts/WeatherContext';

const WeatherForecast = () => {
  const { weather, fetchWeather } = useWeather();
  const [expanded, setExpanded] = useState(false);
  const [forecast, setForecast] = useState([]);
  const [loading, setLoading] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    fetchForecast();
  }, []);

  const fetchForecast = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/weather/forecast', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setForecast(data);
      }
    } catch (error) {
      console.error('Error fetching forecast:', error);
      // Mock forecast data for demo
      setForecast(generateMockForecast());
    } finally {
      setLoading(false);
    }
  };

  const generateMockForecast = () => {
    const hours = [];
    const now = new Date();
    for (let i = 0; i < 24; i += 3) {
      const time = new Date(now.getTime() + i * 60 * 60 * 1000);
      hours.push({
        time: time.toLocaleTimeString('en-US', { hour: 'numeric', hour12: true }),
        temperature: Math.round(20 + Math.random() * 15),
        description: ['Sunny', 'Cloudy', 'Partly Cloudy', 'Light Rain'][Math.floor(Math.random() * 4)],
        humidity: Math.round(40 + Math.random() * 40),
        wind_speed: Math.round(2 + Math.random() * 8)
      });
    }
    return hours;
  };

  const getWeatherIcon = (description) => {
    const icons = {
      'Clear': '☀️',
      'Clouds': '☁️',
      'Rain': '🌧️',
      'Snow': '❄️',
      'Thunderstorm': '⛈️',
      'Drizzle': '🌦️',
      'Mist': '🌫️',
      'Fog': '🌫️',
      'Sunny': '☀️',
      'Partly Cloudy': '⛅',
      'Light Rain': '🌦️'
    };
    return icons[description] || '🌤️';
  };

  const getWeatherColor = (description) => {
    const colors = {
      'Clear': '#1b3b6f',
      'Sunny': '#274472',
      'Clouds': '#334e68',
      'Partly Cloudy': '#3c6e71',
      'Rain': '#0a2463',
      'Light Rain': '#60a5fa',
      'Snow': '#e3f2fd',
      'Thunderstorm': '#06163c'
    };
    return colors[description] || '#334e68';
  };

  const getUVIndex = (time) => {
    const hour = new Date().getHours();
    if (hour >= 10 && hour <= 16) return 'High';
    if (hour >= 7 && hour <= 19) return 'Moderate';
    return 'Low';
  };

  const getAirQuality = () => {
    return 'Good';
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  if (!weather) {
    return (
      <Card elevation={3}>
        <CardContent>
          <Typography variant="body1" color="text.secondary">
            Weather data unavailable
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card elevation={3}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6">
            <Schedule sx={{ mr: 1, verticalAlign: 'middle' }} />
            Weather Forecast
          </Typography>
          <IconButton
            onClick={() => setExpanded(!expanded)}
            sx={{ transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'transform 0.3s' }}
          >
            {expanded ? <ExpandLess /> : <ExpandMore />}
          </IconButton>
        </Box>

        {/* Current Weather Summary */}
        <Box sx={{ mb: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item>
              <Typography variant="h2" sx={{ color: getWeatherColor(weather.main) }}>
                {getWeatherIcon(weather.main)}
              </Typography>
            </Grid>
            <Grid item xs>
              <Typography variant="h4">
                {Math.round(weather.temperature)}°C
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {weather.description}
              </Typography>
            </Grid>
            <Grid item>
              <Box textAlign="right">
                <Chip label={`UV: ${getUVIndex()}`} size="small" color="warning" />
                <Chip label={`Air: ${getAirQuality()}`} size="small" color="success" sx={{ ml: 1 }} />
              </Box>
            </Grid>
          </Grid>
        </Box>

        {/* Weather Details */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={6} sm={3}>
            <Box textAlign="center">
              <Opacity sx={{ fontSize: 30, color: 'primary.main', mb: 1 }} />
              <Typography variant="h6">{weather.humidity}%</Typography>
              <Typography variant="caption">Humidity</Typography>
            </Box>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Box textAlign="center">
              <Air sx={{ fontSize: 30, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h6">{weather.wind_speed} m/s</Typography>
              <Typography variant="caption">Wind</Typography>
            </Box>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Box textAlign="center">
              <Thermostat sx={{ fontSize: 30, color: 'error.main', mb: 1 }} />
              <Typography variant="h6">{Math.round(weather.feels_like)}°C</Typography>
              <Typography variant="caption">Feels Like</Typography>
            </Box>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Box textAlign="center">
              <Visibility sx={{ fontSize: 30, color: 'info.main', mb: 1 }} />
              <Typography variant="h6">10 km</Typography>
              <Typography variant="caption">Visibility</Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Expandable Forecast */}
        <Collapse in={expanded}>
          <Box sx={{ borderTop: 1, borderColor: 'divider', pt: 2 }}>
            <Tabs value={tabValue} onChange={handleTabChange} centered sx={{ mb: 2 }}>
              <Tab label="Hourly" />
              <Tab label="Daily" />
              <Tab label="Details" />
            </Tabs>

            {tabValue === 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  24-Hour Forecast
                </Typography>
                {loading ? (
                  <LinearProgress />
                ) : (
                  <Grid container spacing={1}>
                    {forecast.map((hour, index) => (
                      <Grid item xs={6} sm={3} key={index}>
                        <Card variant="outlined" sx={{ textAlign: 'center', p: 1 }}>
                          <Typography variant="caption" color="text.secondary">
                            {hour.time}
                          </Typography>
                          <Typography variant="h4" sx={{ color: getWeatherColor(hour.description) }}>
                            {getWeatherIcon(hour.description)}
                          </Typography>
                          <Typography variant="h6">
                            {hour.temperature}°C
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {hour.description}
                          </Typography>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                )}
              </Box>
            )}

            {tabValue === 1 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  7-Day Forecast
                </Typography>
                <Grid container spacing={2}>
                  {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
                    <Grid item xs={12} sm={6} md={3} key={day}>
                      <Card variant="outlined" sx={{ textAlign: 'center', p: 2 }}>
                        <Typography variant="h6" gutterBottom>
                          {day}
                        </Typography>
                        <Typography variant="h3" sx={{ color: getWeatherColor('Sunny') }}>
                          ☀️
                        </Typography>
                        <Typography variant="h6">
                          {Math.round(20 + Math.random() * 10)}°C
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Sunny
                        </Typography>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}

            {tabValue === 2 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Weather Details
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Card variant="outlined" sx={{ p: 2 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Sunrise & Sunset
                      </Typography>
                      <Typography variant="body2">
                        🌅 Sunrise: 6:30 AM
                      </Typography>
                      <Typography variant="body2">
                        🌇 Sunset: 6:45 PM
                      </Typography>
                    </Card>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Card variant="outlined" sx={{ p: 2 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Precipitation
                      </Typography>
                      <Typography variant="body2">
                        💧 Chance of Rain: 20%
                      </Typography>
                      <Typography variant="body2">
                        🌧️ Expected: 0.5mm
                      </Typography>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default WeatherForecast; 