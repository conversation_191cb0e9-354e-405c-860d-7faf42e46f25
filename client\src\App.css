/* Enhanced Interactive Campus Map Styles */
.enhanced-map-card {
  background: rgba(13, 27, 42, 0.85) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(102, 153, 204, 0.18) !important;
  border-radius: 20px !important;
  overflow: hidden !important;
  position: relative !important;
}

.enhanced-map-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.05) 0%, rgba(167, 139, 250, 0.05) 100%);
  z-index: 0;
}

.campus-map-container {
  position: relative;
  height: 600px;
  background: linear-gradient(135deg, #0d1b2a 0%, #1b263b 50%, #2c3e50 100%);
  border-radius: 16px;
  overflow: hidden;
  padding: 20px;
}

.campus-map-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 30%, rgba(96, 165, 250, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(167, 139, 250, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.location-marker {
  position: absolute;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.location-marker:hover {
  transform: scale(1.1);
  z-index: 20;
}

.location-marker.selected {
  z-index: 25;
}

.location-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
  border: 3px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.location-marker:hover .location-icon {
  box-shadow: 0 12px 35px rgba(96, 165, 250, 0.6);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.05);
}

.location-label {
  color: #e6f0ff;
  font-weight: bold;
  text-align: center;
  margin-top: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  font-size: 12px;
  white-space: nowrap;
}

/* Pulse Animation for Selected Location */
.pulse-ring {
  position: absolute;
  top: -10px;
  left: -10px;
  width: 80px;
  height: 80px;
  border: 3px solid #60a5fa;
  border-radius: 50%;
  animation: pulseGlow 2s infinite;
  pointer-events: none;
}

@keyframes pulseGlow {
  0% {
    transform: scale(0.8);
    opacity: 1;
    border-color: #60a5fa;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
    border-color: #3b82f6;
  }
  100% {
    transform: scale(1.6);
    opacity: 0;
    border-color: #1d4ed8;
  }
}

/* Connection Lines */
.connection-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, #60a5fa, #a78bfa);
  border-radius: 1px;
  animation: flowingLine 3s infinite;
  z-index: 5;
}

@keyframes flowingLine {
  0% {
    box-shadow: 0 0 5px #60a5fa;
  }
  50% {
    box-shadow: 0 0 20px #a78bfa, 0 0 30px #60a5fa;
  }
  100% {
    box-shadow: 0 0 5px #60a5fa;
  }
}

/* Filter Bar */
.filter-bar {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 30;
}

.filter-button {
  background: rgba(96, 165, 250, 0.2);
  border: 1px solid rgba(96, 165, 250, 0.4);
  color: #e6f0ff;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-size: 12px;
  font-weight: 500;
}

.filter-button:hover {
  background: rgba(96, 165, 250, 0.3);
  border-color: rgba(96, 165, 250, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(96, 165, 250, 0.3);
}

.filter-button.active {
  background: rgba(96, 165, 250, 0.4);
  border-color: #60a5fa;
  box-shadow: 0 0 15px rgba(96, 165, 250, 0.5);
}

/* Floating Action Buttons */
.fab-container {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 30;
}

.fab-container .MuiFab-root {
  background: rgba(96, 165, 250, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.fab-container .MuiFab-root:hover {
  background: rgba(96, 165, 250, 1);
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
}

.refresh-fab {
  background: rgba(34, 197, 94, 0.9) !important;
}

.refresh-fab:hover {
  background: rgba(34, 197, 94, 1) !important;
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4) !important;
}

.suggest-fab {
  background: rgba(249, 115, 22, 0.9) !important;
}

.suggest-fab:hover {
  background: rgba(249, 115, 22, 1) !important;
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4) !important;
}

/* Notification Cards */
.notification-card {
  background: rgba(13, 27, 42, 0.9);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(102, 153, 204, 0.2);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.notification-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
}

.notification-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 10px;
  font-weight: bold;
  margin-bottom: 8px;
}

.notification-tag.weather {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.notification-tag.suggestion {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.notification-tag.alert {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Enhanced Seating Preferences Chart Styles */
.seating-preferences-chart {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.seating-preferences-chart:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 20px 60px rgba(96, 165, 250, 0.25) !important;
}

.chart-bar {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
}

.chart-bar:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4) !important;
}

.chart-container {
  position: relative !important;
  overflow: hidden !important;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 40%, rgba(96, 165, 250, 0.05), transparent 50%),
              radial-gradient(circle at 70% 60%, rgba(167, 139, 250, 0.05), transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Chart Bar Animations */
@keyframes barGrow {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    height: var(--bar-height);
    opacity: 1;
  }
}

.chart-bar-animated {
  animation: barGrow 1s ease-out forwards;
  animation-delay: var(--bar-delay);
}

/* Enhanced Tooltip Styles */
.MuiTooltip-tooltip {
  background: rgba(13, 27, 42, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(96, 165, 250, 0.3) !important;
  color: #e6f0ff !important;
  font-size: 12px !important;
  padding: 12px !important;
  border-radius: 8px !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3) !important;
}

/* Chart Legend Enhancements */
.chart-legend .MuiChip-root {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px) !important;
}

.chart-legend .MuiChip-root:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3) !important;
}

/* Responsive Chart Adjustments */
@media (max-width: 768px) {
  .chart-container {
    height: 150px !important;
  }
  
  .chart-bar {
    transform: scale(0.9) !important;
  }
  
  .chart-bar:hover {
    transform: scale(0.95) !important;
  }
}

@media (max-width: 480px) {
  .chart-container {
    height: 120px !important;
  }
  
  .chart-bar {
    transform: scale(0.8) !important;
  }
  
  .chart-bar:hover {
    transform: scale(0.85) !important;
  }
}

/* General Glassmorphism and Gradient Styles */
.glassmorphism-card {
  background: rgba(13, 27, 42, 0.85);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(102, 153, 204, 0.18);
  border-radius: 20px;
  overflow: hidden;
}

.gradient-text {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.interactive-element {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive-element:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.3);
}

/* Shimmer Effect */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(13, 27, 42, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(96, 165, 250, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(96, 165, 250, 0.7);
}

/* Quick Insights Box */
.quick-insights-box {
  background: rgba(96, 165, 250, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(96, 165, 250, 0.2);
  padding: 16px;
  margin-top: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .campus-map-container {
    height: 400px;
    padding: 15px;
  }
  
  .location-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .filter-bar {
    bottom: 15px;
    gap: 8px;
  }
  
  .filter-button {
    padding: 6px 12px;
    font-size: 11px;
  }
  
  .fab-container {
    top: 15px;
    right: 15px;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .campus-map-container {
    height: 300px;
    padding: 10px;
  }
  
  .location-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .location-label {
    font-size: 10px;
  }
} 