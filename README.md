# Campus Seating Allocation App

A full-stack web application that uses face recognition and weather data to recommend the best place for students to sit on campus.

## Features

- **User Authentication**: Secure login and registration system
- **Face Recognition**: Real-time emotion detection using face-api.js
- **Weather Integration**: Real-time weather data from OpenWeather API
- **Smart Recommendations**: AI-powered seating recommendations based on mood and weather
- **Campus Locations**: Four distinct campus areas with different purposes
- **Modern UI**: Clean Material UI design with responsive layout

## Campus Locations

1. **Peacock Circle** 🎭 - Cultural events and social activities
2. **Second Turf** ⚽ - Sports and physical activities  
3. **Library** 📚 - Quiet study and reading environment
4. **Sandy <PERSON>** 🏖️ - Peaceful relaxation spot

## Technology Stack

### Frontend
- React 18
- Material UI
- face-api.js (TensorFlow.js)
- React Router
- Axios

### Backend
- Node.js
- Express.js
- MongoDB
- JWT Authentication
- bcryptjs
- OpenWeather API

## Installation

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (local or cloud)
- OpenWeather API key

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd campus-seating-app
   ```

2. **Install dependencies**
   ```bash
   npm run install-all
   ```

3. **Environment Configuration**
   
   Create `server/config.env`:
   ```env
   PORT=5000
   MONGODB_URI=mongodb://localhost:27017/campus-seating
   JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
   OPENWEATHER_API_KEY=your-openweather-api-key-here
   NODE_ENV=development
   ```

4. **Get OpenWeather API Key**
   - Visit [OpenWeather API](https://openweathermap.org/api)
   - Sign up for a free account
   - Get your API key
   - Add it to `server/config.env`

5. **Start MongoDB**
   ```bash
   # If using local MongoDB
   mongod
   
   # Or use MongoDB Atlas (cloud)
   ```

6. **Run the application**
   ```bash
   # Development mode (both frontend and backend)
   npm run dev
   
   # Or run separately:
   npm run server  # Backend only
   npm run client  # Frontend only
   ```

7. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## Usage

1. **Register/Login**: Create an account or sign in
2. **Face Recognition**: Allow camera access and click "Detect Emotion"
3. **Weather Check**: Current weather is automatically fetched
4. **Get Recommendation**: The app will suggest the best campus location
5. **View Locations**: Browse all campus areas and their activities

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/preferences` - Update user preferences

### Weather
- `GET /api/weather/current` - Get current weather
- `GET /api/weather/forecast` - Get weather forecast

### Recommendations
- `POST /api/recommendation/generate` - Generate seating recommendation
- `GET /api/recommendation/history` - Get recommendation history

## Project Structure

```
campus-seating-app/
├── client/                 # React frontend
│   ├── public/
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── contexts/       # React contexts
│   │   └── App.js
├── server/                 # Node.js backend
│   ├── models/            # MongoDB models
│   ├── routes/            # API routes
│   ├── middleware/        # Custom middleware
│   └── index.js
├── package.json
└── README.md
```

## Face Recognition Models

The app uses face-api.js models for emotion detection. Models are loaded from the `/models` directory in the public folder.

## Weather Integration

The app integrates with OpenWeather API to provide real-time weather data, which influences indoor/outdoor recommendations.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License

## Support

For support, please open an issue in the repository or contact the development team.

## Future Enhancements

- Mobile app version
- Real-time location tracking
- Social features
- Advanced analytics
- Integration with campus events 