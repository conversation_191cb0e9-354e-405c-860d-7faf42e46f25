import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Typography,
  Box,
  Chip
} from '@mui/material';
import {
  Celebration,
  SportsSoccer,
  LibraryBooks,
  BeachAccess
} from '@mui/icons-material';

const LocationCards = () => {
  const locations = [
    {
      name: 'Peacock Circle',
      icon: <Celebration sx={{ fontSize: 60, color: '#1b3b6f' }} />,
      description: 'Cultural events and social activities',
      activities: ['Cultural Events', 'Social Gatherings', 'Performances'],
      color: '#1b3b6f',
      indoor: true
    },
    {
      name: 'Turf',
      icon: <SportsSoccer sx={{ fontSize: 60, color: '#274472' }} />,
      description: 'Sports and physical activities',
      activities: ['Football', 'Cricket', 'Other Sports'],
      color: '#274472',
      indoor: false
    },
    {
      name: 'Library',
      icon: <LibraryBooks sx={{ fontSize: 60, color: '#0a2463' }} />,
      description: 'Quiet study and reading environment',
      activities: ['Studying', 'Reading', 'Research'],
      color: '#0a2463',
      indoor: true
    },
    {
      name: '<PERSON>',
      icon: <BeachAccess sx={{ fontSize: 60, color: '#3c6e71' }} />,
      description: 'Peaceful relaxation spot',
      activities: ['Relaxation', 'Meditation', 'Casual Reading'],
      color: '#3c6e71',
      indoor: false
    }
  ];

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        Campus Locations
      </Typography>
      
      <Grid container spacing={3}>
        {locations.map((location) => (
          <Grid item xs={12} sm={6} md={3} key={location.name}>
            <Card 
              className="location-card"
              sx={{ 
                height: '100%',
                border: `2px solid ${location.color}20`,
                '&:hover': {
                  borderColor: location.color
                }
              }}
            >
              <CardContent sx={{ textAlign: 'center', p: 3 }}>
                <Box mb={2}>
                  {location.icon}
                </Box>
                
                <Typography variant="h6" gutterBottom sx={{ color: location.color }}>
                  {location.name}
                </Typography>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {location.description}
                </Typography>
                
                <Box display="flex" flexWrap="wrap" gap={1} justifyContent="center">
                  {location.activities.map((activity) => (
                    <Chip
                      key={activity}
                      label={activity}
                      size="small"
                      variant="outlined"
                      sx={{ 
                        borderColor: location.color,
                        color: location.color,
                        '&:hover': {
                          backgroundColor: `${location.color}10`
                        }
                      }}
                    />
                  ))}
                </Box>
                
                <Box mt={2}>
                  <Chip
                    label={location.indoor ? 'Indoor' : 'Outdoor'}
                    size="small"
                    color={location.indoor ? 'primary' : 'secondary'}
                    variant="filled"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default LocationCards; 