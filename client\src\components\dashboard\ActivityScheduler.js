import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider
} from '@mui/material';
import {
  Add,
  Schedule,
  LocationOn,
  Delete,
  Edit,
  PlayArrow,
  Stop
} from '@mui/icons-material';

const ActivityScheduler = () => {
  const [activities, setActivities] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingActivity, setEditingActivity] = useState(null);
  const [currentActivity, setCurrentActivity] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    location: '',
    startTime: '',
    endTime: '',
    type: '',
    description: ''
  });

  const activityTypes = [
    { value: 'study', label: 'Study Session', icon: '📚', color: '#2196f3' },
    { value: 'social', label: 'Social Activity', icon: '👥', color: '#e91e63' },
    { value: 'sports', label: 'Sports/Exercise', icon: '⚽', color: '#4caf50' },
    { value: 'relaxation', label: 'Relaxation', icon: '🧘', color: '#ff9800' },
    { value: 'work', label: 'Work/Project', icon: '💼', color: '#9c27b0' },
    { value: 'other', label: 'Other', icon: '📝', color: '#607d8b' }
  ];

  const locations = [
    'Peacock Circle',
    'Second Turf',
    'Library',
    'Sandy Dunes',
    'Cafeteria',
    'Classroom',
    'Home',
    'Other'
  ];

  const handleAddActivity = () => {
    if (editingActivity !== null) {
      // Edit existing activity
      const updatedActivities = activities.map((activity, index) =>
        index === editingActivity ? { ...formData, id: Date.now() } : activity
      );
      setActivities(updatedActivities);
      setEditingActivity(null);
    } else {
      // Add new activity
      setActivities([...activities, { ...formData, id: Date.now() }]);
    }
    handleCloseDialog();
  };

  const handleEditActivity = (index) => {
    setEditingActivity(index);
    setFormData(activities[index]);
    setOpenDialog(true);
  };

  const handleDeleteActivity = (index) => {
    const updatedActivities = activities.filter((_, i) => i !== index);
    setActivities(updatedActivities);
  };

  const handleStartActivity = (activity) => {
    setCurrentActivity(activity);
  };

  const handleStopActivity = () => {
    setCurrentActivity(null);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingActivity(null);
    setFormData({
      title: '',
      location: '',
      startTime: '',
      endTime: '',
      type: '',
      description: ''
    });
  };

  const getActivityTypeInfo = (type) => {
    return activityTypes.find(t => t.value === type) || activityTypes[5];
  };

  const getSortedActivities = () => {
    return [...activities].sort((a, b) => {
      const timeA = new Date(`2000-01-01 ${a.startTime}`);
      const timeB = new Date(`2000-01-01 ${b.startTime}`);
      return timeA - timeB;
    });
  };

  const getTimeSlotRecommendation = (time) => {
    const hour = parseInt(time.split(':')[0]);
    if (hour >= 6 && hour <= 10) return 'Morning - Great for focused study!';
    if (hour >= 11 && hour <= 14) return 'Noon - Perfect for social activities!';
    if (hour >= 15 && hour <= 18) return 'Afternoon - Ideal for sports and exercise!';
    if (hour >= 19 && hour <= 22) return 'Evening - Relaxation time!';
    return 'Late night - Consider rest!';
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        <Schedule sx={{ mr: 1, verticalAlign: 'middle' }} />
        Activity Scheduler
      </Typography>

      <Card elevation={3}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6">
              Today's Schedule
            </Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setOpenDialog(true)}
            >
              Add Activity
            </Button>
          </Box>

          {activities.length === 0 ? (
            <Box textAlign="center" py={4}>
              <Typography variant="body1" color="text.secondary" gutterBottom>
                No activities scheduled yet
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Click "Add Activity" to start planning your day
              </Typography>
            </Box>
          ) : (
            <List>
              {getSortedActivities().map((activity, index) => {
                const typeInfo = getActivityTypeInfo(activity.type);
                const isCurrent = currentActivity?.id === activity.id;
                
                return (
                  <React.Fragment key={activity.id}>
                    <ListItem
                      sx={{
                        border: isCurrent ? '2px solid #4caf50' : '1px solid #e0e0e0',
                        borderRadius: 2,
                        mb: 1,
                        backgroundColor: isCurrent ? '#e8f5e8' : 'transparent',
                        transition: 'all 0.3s ease'
                      }}
                    >
                      <ListItemIcon>
                        <Typography variant="h4">
                          {typeInfo.icon}
                        </Typography>
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="h6">
                              {activity.title}
                            </Typography>
                            {isCurrent && (
                              <Chip
                                label="In Progress"
                                color="success"
                                size="small"
                                icon={<PlayArrow />}
                              />
                            )}
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              <LocationOn sx={{ fontSize: 16, verticalAlign: 'middle', mr: 0.5 }} />
                              {activity.location}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {activity.startTime} - {activity.endTime}
                            </Typography>
                            {activity.description && (
                              <Typography variant="body2" color="text.secondary">
                                {activity.description}
                              </Typography>
                            )}
                            <Chip
                              label={getTimeSlotRecommendation(activity.startTime)}
                              size="small"
                              variant="outlined"
                              sx={{ mt: 1 }}
                            />
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Box display="flex" gap={1}>
                          {!isCurrent ? (
                            <IconButton
                              onClick={() => handleStartActivity(activity)}
                              color="success"
                              size="small"
                            >
                              <PlayArrow />
                            </IconButton>
                          ) : (
                            <IconButton
                              onClick={handleStopActivity}
                              color="error"
                              size="small"
                            >
                              <Stop />
                            </IconButton>
                          )}
                          <IconButton
                            onClick={() => handleEditActivity(index)}
                            size="small"
                          >
                            <Edit />
                          </IconButton>
                          <IconButton
                            onClick={() => handleDeleteActivity(index)}
                            color="error"
                            size="small"
                          >
                            <Delete />
                          </IconButton>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < activities.length - 1 && <Divider />}
                  </React.Fragment>
                );
              })}
            </List>
          )}

          {activities.length > 0 && (
            <Box mt={3} p={2} bgcolor="grey.50" borderRadius={2}>
              <Typography variant="subtitle2" gutterBottom>
                Schedule Summary
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6} sm={3}>
                  <Typography variant="body2" color="text.secondary">
                    Total Activities: {activities.length}
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Typography variant="body2" color="text.secondary">
                    Study Time: {activities.filter(a => a.type === 'study').length} sessions
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Typography variant="body2" color="text.secondary">
                    Social Time: {activities.filter(a => a.type === 'social').length} activities
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Typography variant="body2" color="text.secondary">
                    Active Time: {activities.filter(a => a.type === 'sports').length} sessions
                  </Typography>
                </Grid>
              </Grid>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Activity Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingActivity !== null ? 'Edit Activity' : 'Add New Activity'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Activity Title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Activity Type</InputLabel>
                <Select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                  label="Activity Type"
                >
                  {activityTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography>{type.icon}</Typography>
                        <Typography>{type.label}</Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Location</InputLabel>
                <Select
                  value={formData.location}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  label="Location"
                >
                  {locations.map((location) => (
                    <MenuItem key={location} value={location}>
                      {location}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Start Time"
                type="time"
                value={formData.startTime}
                onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="End Time"
                type="time"
                value={formData.endTime}
                onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description (Optional)"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleAddActivity}
            disabled={!formData.title || !formData.type || !formData.location || !formData.startTime}
          >
            {editingActivity !== null ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ActivityScheduler; 