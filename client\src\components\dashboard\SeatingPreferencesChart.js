import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Tooltip,
  IconButton,
  Chip
} from '@mui/material';
import {
  Info,
  TrendingUp
} from '@mui/icons-material';

const SeatingPreferencesChart = () => {
  const [hoveredBar, setHoveredBar] = useState(null);

  const seatingData = [
    { location: 'Library', percentage: 35, color: '#2196f3', description: 'Quiet study environment' },
    { location: 'Peacock Circle', percentage: 25, color: '#e91e63', description: 'Social gathering spot' },
    { location: 'Second Turf', percentage: 20, color: '#4caf50', description: 'Sports and activities' },
    { location: 'Sandy Dunes', percentage: 15, color: '#ff9800', description: 'Peaceful relaxation' },
    { location: 'Others', percentage: 5, color: '#9c27b0', description: 'Various other locations' }
  ];

  const maxPercentage = Math.max(...seatingData.map(item => item.percentage));

  const handleBarHover = (index) => {
    setHoveredBar(index);
  };

  const handleBarLeave = () => {
    setHoveredBar(null);
  };

  return (
    <Card 
      elevation={0}
      sx={{
        background: 'rgba(13, 27, 42, 0.85)',
        backdropFilter: 'blur(20px)',
        border: '1px solid rgba(102, 153, 204, 0.18)',
        borderRadius: '20px',
        overflow: 'hidden',
        height: '100%',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'linear-gradient(135deg, rgba(96, 165, 250, 0.05) 0%, rgba(167, 139, 250, 0.05) 100%)',
          zIndex: 0
        }
      }}
    >
      <CardContent sx={{ p: 3, position: 'relative', zIndex: 1 }}>
        {/* Header */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
          <Box display="flex" alignItems="center">
            <TrendingUp sx={{ mr: 1.5, color: '#60a5fa' }} />
            <Typography 
              variant="h6" 
              sx={{ 
                color: '#e6f0ff',
                fontWeight: 'bold'
              }}
            >
              Seating Preferences (Student Choices)
            </Typography>
          </Box>
          <Tooltip 
            title="This data shows the percentage of students choosing each campus location to sit."
            arrow
            placement="top"
          >
            <IconButton size="small" sx={{ color: '#60a5fa' }}>
              <Info />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Chart Container */}
        <Box sx={{ position: 'relative', height: '200px', mb: 2 }}>
          {/* Chart Bars */}
          <Box 
            display="flex" 
            alignItems="end" 
            justifyContent="space-between" 
            height="100%"
            gap={1}
          >
            {seatingData.map((item, index) => (
              <Tooltip
                key={item.location}
                title={
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      {item.location}
                    </Typography>
                    <Typography variant="body2">
                      {item.percentage}% of students
                    </Typography>
                    <Typography variant="caption" sx={{ opacity: 0.8 }}>
                      {item.description}
                    </Typography>
                  </Box>
                }
                arrow
                placement="top"
              >
                <Box
                  onMouseEnter={() => handleBarHover(index)}
                  onMouseLeave={handleBarLeave}
                  sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    cursor: 'pointer',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    transform: hoveredBar === index ? 'scale(1.05)' : 'scale(1)',
                  }}
                >
                  {/* Bar */}
                  <Box
                    sx={{
                      width: '100%',
                      height: `${(item.percentage / maxPercentage) * 100}%`,
                      background: `linear-gradient(180deg, ${item.color} 0%, ${item.color}80 100%)`,
                      borderRadius: '8px 8px 0 0',
                      boxShadow: hoveredBar === index 
                        ? `0 8px 25px ${item.color}40` 
                        : `0 4px 15px ${item.color}20`,
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      border: hoveredBar === index 
                        ? `2px solid ${item.color}` 
                        : '2px solid transparent',
                      position: 'relative',
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: '20%',
                        background: `linear-gradient(180deg, ${item.color}40 0%, transparent 100%)`,
                        borderRadius: '8px 8px 0 0',
                      }
                    }}
                  />
                  
                  {/* Percentage Label */}
                  <Typography
                    variant="caption"
                    sx={{
                      color: '#e6f0ff',
                      fontWeight: 'bold',
                      mt: 1,
                      fontSize: '12px',
                      textAlign: 'center',
                      opacity: hoveredBar === index ? 1 : 0.8,
                      transition: 'opacity 0.3s ease'
                    }}
                  >
                    {item.percentage}%
                  </Typography>
                </Box>
              </Tooltip>
            ))}
          </Box>

          {/* Y-axis labels */}
          <Box
            sx={{
              position: 'absolute',
              left: '-30px',
              top: 0,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
              color: '#60a5fa',
              fontSize: '10px',
              fontWeight: 'bold'
            }}
          >
            <span>100%</span>
            <span>75%</span>
            <span>50%</span>
            <span>25%</span>
            <span>0%</span>
          </Box>
        </Box>

        {/* Legend */}
        <Box display="flex" flexWrap="wrap" gap={1} justifyContent="center">
          {seatingData.map((item) => (
            <Chip
              key={item.location}
              label={item.location}
              size="small"
              sx={{
                backgroundColor: `${item.color}20`,
                color: '#e6f0ff',
                border: `1px solid ${item.color}40`,
                fontSize: '10px',
                '&:hover': {
                  backgroundColor: `${item.color}30`,
                  transform: 'translateY(-1px)',
                  boxShadow: `0 4px 12px ${item.color}30`
                },
                transition: 'all 0.3s ease'
              }}
            />
          ))}
        </Box>

        {/* Summary Stats */}
        <Box 
          mt={2} 
          p={2} 
          sx={{
            background: 'rgba(96, 165, 250, 0.1)',
            borderRadius: '12px',
            border: '1px solid rgba(96, 165, 250, 0.2)'
          }}
        >
          <Typography variant="body2" sx={{ color: '#60a5fa', fontWeight: 'bold', mb: 1 }}>
            Quick Insights
          </Typography>
          <Typography variant="caption" sx={{ color: '#e6f0ff', opacity: 0.8 }}>
            Library is the most popular choice with {seatingData[0].percentage}% of students, 
            followed by Peacock Circle at {seatingData[1].percentage}%. 
            The data shows a strong preference for quiet study areas and social spaces.
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default SeatingPreferencesChart; 