import React, { useState } from 'react';
import {
  Con<PERSON><PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  AppBar,
  <PERSON><PERSON><PERSON>,
  IconButton,
  <PERSON>,
  Alert,
  Fade
} from '@mui/material';
import { API_ENDPOINTS } from '../../config/api';
import {
  Logout,
  CameraAlt,
  Map,
  Psychology,
  Schedule,
  Dashboard as DashboardIcon,
  WbSunny
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import FaceRecognition from './FaceRecognition';
import SeatingPreferencesChart from './SeatingPreferencesChart';
import LocationCards from './LocationCards';
import RecommendationDisplay from './RecommendationDisplay';
import InteractiveMap from './InteractiveMap';
import EmotionQuiz from './EmotionQuiz';
import ActivityScheduler from './ActivityScheduler';
import NotificationSystem from '../common/NotificationSystem';
import WeatherDisplay from './WeatherDisplay';
import WeatherForecast from './WeatherForecast';

const Dashboard = () => {
  const { user, logout } = useAuth();
  const [currentEmotion, setCurrentEmotion] = useState(null);
  const [recommendation, setRecommendation] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const tabList = [
    {
      label: 'Dashboard',
      icon: <DashboardIcon />, 
      component: 'dashboard'
    },
    {
      label: 'Interactive Map',
      icon: <Map />,
      component: 'map'
    },
    {
      label: 'Emotion Quiz',
      icon: <Psychology />,
      component: 'quiz'
    },
    {
      label: 'Activity Scheduler',
      icon: <Schedule />,
      component: 'activity'
    },
    {
      label: 'Weather',
      icon: <WbSunny />,
      component: 'weather'
    }
  ];

  const handleEmotionDetected = async (emotion) => {
    setCurrentEmotion(emotion);
    setLoading(true);
    setError('');

    try {
              const response = await fetch(`${API_ENDPOINTS.RECOMMENDATION.GET}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          emotion
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get recommendation');
      }

      const data = await response.json();
      setRecommendation(data);
    } catch (err) {
      setError('Failed to get recommendation. Please try again.');
      console.error('Recommendation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
  };

  const handleTabChange = (idx) => {
    setActiveTab(idx);
  };

  const handleEmotionFromQuiz = (emotion) => {
    setCurrentEmotion(emotion);
    // Automatically trigger recommendation
    handleEmotionDetected(emotion);
  };

  const getEmotionColor = (emotion) => {
    switch (emotion?.toLowerCase()) {
      case 'happy':
      case 'excited':
      case 'energetic':
        return 'success';
      case 'tired':
      case 'dull':
      case 'sad':
        return 'warning';
      case 'focused':
      case 'concentrated':
        return 'info';
      case 'relaxed':
      case 'calm':
        return 'primary';
      default:
        return 'default';
    }
  };

  const renderTabContent = () => {
    return (
      <>
        <Fade in={activeTab === 0} timeout={500} unmountOnExit>
          <div style={{ width: '100%' }}>
            <Grid container spacing={3}>
              {/* Face Recognition Section */}
              <Grid item xs={12} md={6}>
                <Card elevation={3}>
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={2}>
                      <CameraAlt sx={{ mr: 1 }} />
                      <Typography variant="h6">Face Recognition</Typography>
                    </Box>
                    <FaceRecognition onEmotionDetected={handleEmotionDetected} />
                    {currentEmotion && (
                      <Box mt={2}>
                        <Chip
                          label={`Detected: ${currentEmotion}`}
                          color={getEmotionColor(currentEmotion)}
                          variant="outlined"
                        />
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* Seating Preferences Chart */}
              <Grid item xs={12} md={6}>
                <SeatingPreferencesChart />
              </Grid>

              {/* Recommendation Section */}
              {recommendation && (
                <Grid item xs={12}>
                  <RecommendationDisplay 
                    recommendation={recommendation}
                    loading={loading}
                  />
                </Grid>
              )}

              {/* Campus Locations */}
              <Grid item xs={12}>
                <LocationCards />
              </Grid>
            </Grid>
          </div>
        </Fade>
        <Fade in={activeTab === 1} timeout={500} unmountOnExit>
          <div style={{ width: '100%' }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <InteractiveMap onLocationSelect={(location) => {
                  console.log('Selected location:', location);
                }} />
              </Grid>
            </Grid>
          </div>
        </Fade>
        <Fade in={activeTab === 2} timeout={500} unmountOnExit>
          <div style={{ width: '100%' }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <EmotionQuiz onEmotionResult={handleEmotionFromQuiz} />
              </Grid>
              {currentEmotion && (
                <Grid item xs={12}>
                  <Card elevation={3}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Quiz Result
                      </Typography>
                      <Chip
                        label={`Detected Emotion: ${currentEmotion}`}
                        color={getEmotionColor(currentEmotion)}
                        variant="outlined"
                        size="large"
                      />
                    </CardContent>
                  </Card>
                </Grid>
              )}
            </Grid>
          </div>
        </Fade>
        <Fade in={activeTab === 3} timeout={500} unmountOnExit>
          <div style={{ width: '100%' }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <ActivityScheduler />
              </Grid>
            </Grid>
          </div>
        </Fade>
        <Fade in={activeTab === 4} timeout={500} unmountOnExit>
          <div style={{ width: '100%' }}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <WeatherDisplay />
              </Grid>
              <Grid item xs={12} md={6}>
                <WeatherForecast />
              </Grid>
            </Grid>
          </div>
        </Fade>
      </>
    );
  };

  return (
    <div className="dashboard-container">
      <AppBar position="static" elevation={0}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Campus Seating App
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2">
              Welcome, {user?.username}!
            </Typography>
            <NotificationSystem />
            <IconButton color="inherit" onClick={handleLogout}>
              <Logout />
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Custom Tab Navigation */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 2,
          mb: 3,
          background: 'rgba(13, 27, 42, 0.85)',
          borderRadius: '16px',
          p: 1.5,
          boxShadow: '0 2px 12px rgba(96, 165, 250, 0.08)'
        }}>
          {tabList.map((tab, idx) => (
            <button
              key={tab.label}
              type="button"
              onClick={() => handleTabChange(idx)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '12px 24px',
                cursor: 'pointer',
                borderRadius: '12px',
                fontWeight: activeTab === idx ? 700 : 500,
                fontSize: '1rem',
                color: activeTab === idx ? '#3b82f6' : '#e6f0ff',
                background: activeTab === idx ? 'linear-gradient(90deg, #e0e7ff 60%, #bae6fd 100%)' : 'none',
                boxShadow: activeTab === idx ? '0 2px 8px #60a5fa22' : 'none',
                border: 'none',
                borderBottom: activeTab === idx ? '3px solid #3b82f6' : '3px solid transparent',
                transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
                outline: 'none',
              }}
              onMouseOver={e => {
                e.currentTarget.style.transform = 'scale(1.07)';
                e.currentTarget.style.color = '#60a5fa';
                e.currentTarget.style.background = 'linear-gradient(90deg, #bae6fd 60%, #e0e7ff 100%)';
                e.currentTarget.style.boxShadow = '0 4px 16px #60a5fa33';
              }}
              onMouseOut={e => {
                e.currentTarget.style.transform = 'scale(1)';
                e.currentTarget.style.color = activeTab === idx ? '#3b82f6' : '#e6f0ff';
                e.currentTarget.style.background = activeTab === idx ? 'linear-gradient(90deg, #e0e7ff 60%, #bae6fd 100%)' : 'none';
                e.currentTarget.style.boxShadow = activeTab === idx ? '0 2px 8px #60a5fa22' : 'none';
              }}
              aria-current={activeTab === idx ? 'page' : undefined}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </Box>

        {/* Tab Content */}
        {renderTabContent()}
      </Container>
    </div>
  );
};

export default Dashboard; 