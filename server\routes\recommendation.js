const express = require('express');
const User = require('../models/User');
const { verifyToken } = require('../middleware/auth');

const router = express.Router();

// Get recommendation based on emotion only
router.post('/generate', verifyToken, async (req, res) => {
  try {
    const { emotion } = req.body;
    
    if (!emotion) {
      return res.status(400).json({ error: 'Emotion data is required' });
    }

    // Recommendation logic based on emotion only
    let recommendedLocation = getRecommendation(emotion);
    
    // Get user preferences
    const user = await User.findById(req.userId);
    if (user && user.preferences.favoriteLocation) {
      // Consider user preference but still use the algorithm
      recommendedLocation = considerUserPreference(recommendedLocation, user.preferences.favoriteLocation);
    }

    // Save to user history
    await User.findByIdAndUpdate(req.userId, {
      $push: {
        history: {
          emotion,
          recommendedLocation
        }
      }
    });

    res.json({
      recommendedLocation,
      emotion,
      reasoning: getReasoning(emotion, recommendedLocation)
    });
  } catch (error) {
    console.error('Recommendation error:', error);
    res.status(500).json({ error: 'Failed to generate recommendation' });
  }
});

// Get user recommendation history
router.get('/history', verifyToken, async (req, res) => {
  try {
    const user = await User.findById(req.userId).select('history');
    res.json(user.history || []);
  } catch (error) {
    console.error('History error:', error);
    res.status(500).json({ error: 'Failed to fetch history' });
  }
});

// Helper function to get recommendation based on emotion only
function getRecommendation(emotion) {
  switch (emotion.toLowerCase()) {
    case 'happy':
    case 'excited':
    case 'active':
      return 'Peacock Circle';
    
    case 'tired':
    case 'dull':
    case 'sad':
      return 'Library';
    
    case 'focused':
    case 'concentrated':
      return 'Library';
    
    case 'relaxed':
    case 'calm':
      return 'Sandy Dunes';
    
    case 'energetic':
    case 'motivated':
      return 'Second Turf';
    
    default:
      return 'Library';
  }
}

// Helper function to consider user preference
function considerUserPreference(algorithmRecommendation, userPreference) {
  // 30% chance to use user preference instead of algorithm
  if (Math.random() < 0.3) {
    return userPreference;
  }
  return algorithmRecommendation;
}

// Helper function to get reasoning
function getReasoning(emotion, location) {
  const emotionText = {
    'happy': 'You seem happy and energetic',
    'excited': 'You appear excited and full of energy',
    'active': 'You look active and ready for action',
    'tired': 'You seem tired and need a quiet place',
    'dull': 'You appear dull and might need some stimulation',
    'sad': 'You look sad and could use a peaceful environment',
    'focused': 'You appear focused and concentrated',
    'concentrated': 'You seem concentrated and need a study environment',
    'relaxed': 'You look relaxed and calm',
    'calm': 'You appear calm and peaceful',
    'energetic': 'You seem energetic and ready for activities',
    'motivated': 'You look motivated and driven'
  };

  const locationText = {
    'Peacock Circle': 'Peacock Circle is perfect for social activities and cultural events',
    'Second Turf': 'Second Turf is ideal for sports and physical activities',
    'Library': 'The Library provides a quiet environment for studying and reading',
    'Sandy Dunes': 'Sandy Dunes offers a peaceful place for relaxation'
  };

  return `${emotionText[emotion.toLowerCase()] || 'Based on your current state'}. ${locationText[location]}.`;
}

module.exports = router; 