import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Fab,
  Grid,
  Zoom,
  Grow,
  GlobalStyles
} from '@mui/material';
import {
  Celebration,
  LocationOn,
  Directions,
  Refresh,
  Lightbulb,
  Home,
  Close,
  WbSunny,
  VolumeOff,
  CampaignOutlined
} from '@mui/icons-material';

const InteractiveMap = ({ onLocationSelect }) => {
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [hoveredLocation, setHoveredLocation] = useState(null);
  const [activeFilter, setActiveFilter] = useState('all');
  const [showConnections, setShowConnections] = useState(false);
  const [notifications] = useState([
    { id: 1, type: 'weather', message: 'Sunny weather perfect for outdoor activities!', color: '#0a2463' },
    { id: 2, type: 'suggestion', message: 'Library is less crowded right now', color: '#274472' },
    { id: 3, type: 'event', message: 'Cultural event starting at Peacock Circle in 30 mins', color: '#1b3b6f' }
  ]);

  // Enhanced location data with more detailed information
  const locations = [
    {
      id: 'peacock-circle',
      name: 'Peacock Circle',
      type: 'active',
      environment: 'indoor',
      icon: (
        <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
          <ellipse cx="20" cy="20" rx="18" ry="12" fill="#1b3b6f" opacity="0.2" />
          <path d="M10 20 Q20 10 30 20 Q20 30 10 20 Z" fill="#1b3b6f" />
        </svg>
      ),
      description: 'The heart of campus culture and social activities',
      activities: ['Cultural Events', 'Social Gatherings', 'Performances', 'Food Festivals'],
      color: '#1b3b6f',
      indoor: true,
      capacity: '200+ people',
      bestTime: 'Evenings & Weekends',
      coordinates: { x: 25, y: 35 },
      seatingComfort: 'High',
      noiseLevel: 'Moderate',
      moodSuitability: 'Social & Energetic',
      currentCrowd: 'Medium',
      details: {
        facilities: ['Amphitheater', 'Food Court', 'WiFi', 'Power Outlets'],
        events: ['Cultural Nights', 'Music Performances', 'Art Exhibitions'],
        tips: 'Best spot for meeting new people and enjoying campus life'
      },
      connections: ['library']
    },
    {
      id: 'second-turf',
      name: 'Second Turf',
      type: 'active',
      environment: 'outdoor',
      icon: (
        <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
          <circle cx="20" cy="20" r="16" fill="#274472" opacity="0.2" />
          <circle cx="20" cy="20" r="10" fill="#274472" />
          <rect x="15" y="15" width="10" height="10" fill="#fff" opacity="0.5" />
        </svg>
      ),
      description: 'Sports complex for physical activities and games',
      activities: ['Football', 'Cricket', 'Basketball', 'Tennis'],
      color: '#274472',
      indoor: false,
      capacity: '100+ people',
      bestTime: 'Mornings & Evenings',
      coordinates: { x: 75, y: 65 },
      seatingComfort: 'Medium',
      noiseLevel: 'High',
      moodSuitability: 'Active & Competitive',
      currentCrowd: 'Low',
      details: {
        facilities: ['Multiple Courts', 'Equipment Rental', 'Changing Rooms', 'Water Dispensers'],
        events: ['Inter-college Tournaments', 'Fitness Classes', 'Sports Camps'],
        tips: 'Perfect for staying active and building team spirit'
      },
      connections: ['sandy-dunes']
    },
    {
      id: 'library',
      name: 'Library',
      type: 'quiet',
      environment: 'indoor',
      icon: (
        <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
          <rect x="8" y="12" width="24" height="16" rx="4" fill="#0a2463" opacity="0.2" />
          <rect x="12" y="16" width="16" height="8" rx="2" fill="#0a2463" />
          <rect x="16" y="18" width="8" height="4" fill="#fff" opacity="0.5" />
        </svg>
      ),
      description: 'Quiet study environment with extensive resources',
      activities: ['Studying', 'Reading', 'Research', 'Group Projects'],
      color: '#0a2463',
      indoor: true,
      capacity: '150+ people',
      bestTime: 'All Day',
      coordinates: { x: 45, y: 75 },
      seatingComfort: 'Very High',
      noiseLevel: 'Very Low',
      moodSuitability: 'Focused & Calm',
      currentCrowd: 'High',
      details: {
        facilities: ['Study Rooms', 'Computer Lab', 'Printing Services', 'Quiet Zones'],
        events: ['Study Groups', 'Research Workshops', 'Book Clubs'],
        tips: 'Best for focused study sessions and academic work'
      },
      connections: ['peacock-circle', 'sandy-dunes']
    },
    {
      id: 'sandy-dunes',
      name: 'Sandy Dunes',
      type: 'quiet',
      environment: 'outdoor',
      icon: (
        <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
          <ellipse cx="20" cy="30" rx="14" ry="6" fill="#3c6e71" opacity="0.2" />
          <path d="M10 30 Q20 20 30 30 Q20 40 10 30 Z" fill="#3c6e71" />
        </svg>
      ),
      description: 'Peaceful relaxation spot with natural beauty',
      activities: ['Relaxation', 'Meditation', 'Casual Reading', 'Nature Walks'],
      color: '#3c6e71',
      indoor: false,
      capacity: '50+ people',
      bestTime: 'Sunrise & Sunset',
      coordinates: { x: 80, y: 25 },
      seatingComfort: 'High',
      noiseLevel: 'Very Low',
      moodSuitability: 'Peaceful & Reflective',
      currentCrowd: 'Low',
      details: {
        facilities: ['Benches', 'Shade Trees', 'Walking Paths', 'Garden Area'],
        events: ['Meditation Sessions', 'Nature Photography', 'Outdoor Reading'],
        tips: 'Ideal for unwinding and connecting with nature'
      },
      connections: ['library', 'second-turf']
    }
  ];

  const filterOptions = [
    { id: 'all', label: 'All Spots', icon: <LocationOn /> },
    { id: 'indoor', label: 'Indoor', icon: <Home /> },
    { id: 'outdoor', label: 'Outdoor', icon: <WbSunny /> },
    { id: 'quiet', label: 'Quiet', icon: <VolumeOff /> },
    { id: 'active', label: 'Active', icon: <CampaignOutlined /> }
  ];

  const filteredLocations = locations.filter(location => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'indoor') return location.indoor;
    if (activeFilter === 'outdoor') return !location.indoor;
    if (activeFilter === 'quiet') return location.type === 'quiet';
    if (activeFilter === 'active') return location.type === 'active';
    return true;
  });

  const handleLocationClick = (location) => {
    setSelectedLocation(location);
    setOpenDialog(true);
    setShowConnections(true);
    if (onLocationSelect) {
      onLocationSelect(location);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedLocation(null);
    setShowConnections(false);
  };

  const handleLocationHover = (location) => {
    setHoveredLocation(location);
  };

  const handleLocationLeave = () => {
    setHoveredLocation(null);
  };

  const handleRefreshMap = () => {
    setShowConnections(false);
    setTimeout(() => setShowConnections(true), 500);
  };

  const handleSuggestSpot = () => {
    const randomLocation = locations[Math.floor(Math.random() * locations.length)];
    handleLocationClick(randomLocation);
  };

  const renderConnectionLines = () => {
    if (!showConnections || !selectedLocation) return null;

    const lines = selectedLocation.connections?.map(connectionId => {
      const connectedLocation = locations.find(loc => loc.id === connectionId);
      if (!connectedLocation) return null;

      return {
        id: connectionId,
        startX: selectedLocation.coordinates.x,
        startY: selectedLocation.coordinates.y,
        endX: connectedLocation.coordinates.x,
        endY: connectedLocation.coordinates.y,
        startColor: selectedLocation.color,
        endColor: connectedLocation.color
      };
    }).filter(Boolean);

    if (!lines || lines.length === 0) return null;

    return (
      <svg viewBox="0 0 100 100" preserveAspectRatio="none" style={{ position: 'absolute', inset: 0, zIndex: 1, pointerEvents: 'none' }}>
        <defs>
          {lines.map((line) => (
            <linearGradient id={`grad-${selectedLocation.id}-${line.id}`} key={`grad-${selectedLocation.id}-${line.id}`} x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor={line.startColor} />
              <stop offset="100%" stopColor={line.endColor} />
            </linearGradient>
          ))}
        </defs>
        {lines.map((line) => (
          <line
            key={`line-${selectedLocation.id}-${line.id}`}
            x1={line.startX}
            y1={line.startY}
            x2={line.endX}
            y2={line.endY}
            stroke={`url(#grad-${selectedLocation.id}-${line.id})`}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeDasharray="4 6"
            style={{ animation: 'dash 1.5s linear infinite' }}
            opacity="0.85"
          />
        ))}
      </svg>
    );
  };

  return (
    <Box sx={{ position: 'relative' }}>
      <GlobalStyles styles={{
        '@keyframes pulseGlow': {
          '0%': { transform: 'translate(-50%, -50%) scale(0.9)', opacity: 0.7 },
          '50%': { transform: 'translate(-50%, -50%) scale(1.1)', opacity: 0.2 },
          '100%': { transform: 'translate(-50%, -50%) scale(0.9)', opacity: 0.7 }
        },
        '@keyframes float': {
          '0%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-8px)' },
          '100%': { transform: 'translateY(0)' }
        },
        '@keyframes dash': {
          '0%': { strokeDashoffset: 100 },
          '100%': { strokeDashoffset: 0 }
        }
      }} />
      {/* Enhanced Title */}
      <Typography 
        variant="h4" 
        gutterBottom 
        sx={{ 
          mb: 3,
          background: 'linear-gradient(45deg, #60a5fa, #a78bfa)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 'bold',
          textAlign: 'center'
        }}
      >
        Interactive Campus Map
      </Typography>

      {/* Notifications Panel */}
      <Card 
        elevation={0}
        sx={{ 
          mb: 3,
          background: 'rgba(13, 27, 42, 0.85)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(102, 153, 204, 0.18)',
          borderRadius: '16px',
        }}
      >
        <CardContent sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom sx={{ color: '#e6f0ff', mb: 2 }}>
            Campus Updates
          </Typography>
          <Grid container spacing={2}>
            {notifications.map(notification => (
              <Grid item xs={12} md={4} key={notification.id}>
                <Card
                  elevation={0}
                  sx={{
                    background: `linear-gradient(135deg, ${notification.color}20, ${notification.color}10)`,
                    border: `1px solid ${notification.color}40`,
                    borderRadius: '12px',
                    p: 1.5
                  }}
                >
                  <Box display="flex" alignItems="center" gap={1}>
                    {notification.type === 'weather' && <WbSunny sx={{ color: notification.color }} />}
                    {notification.type === 'suggestion' && <Lightbulb sx={{ color: notification.color }} />}
                    {notification.type === 'event' && <Celebration sx={{ color: notification.color }} />}
                    <Typography variant="body2" sx={{ color: '#e6f0ff', flex: 1 }}>
                      {notification.message}
                    </Typography>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Enhanced Map Container */}
      <Card 
        elevation={0}
        className="enhanced-map-card"
        sx={{ 
          position: 'relative', 
          minHeight: '500px',
          background: 'rgba(13, 27, 42, 0.85)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(102, 153, 204, 0.18)',
          borderRadius: '20px',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(135deg, rgba(96, 165, 250, 0.1) 0%, rgba(167, 139, 250, 0.1) 100%)',
            zIndex: 0
          }
        }}
      >
        <CardContent sx={{ p: 3, position: 'relative', zIndex: 1 }}>
          {/* Enhanced Campus Map Background */}
          <Box
            className="campus-map-container"
            sx={{
              position: 'relative',
              width: '100%',
              height: '400px',
              background: 'linear-gradient(135deg, #0a1929 0%, #1a237e 50%, #0d47a1 100%)',
              borderRadius: '16px',
              border: '2px solid rgba(96, 165, 250, 0.3)',
              overflow: 'hidden',
              boxShadow: 'inset 0 0 50px rgba(96, 165, 250, 0.1)',
            }}
          >
            {/* Animated Background Elements */}
            {[...Array(6)].map((_, i) => (
              <Box
                key={i}
                className="floating-particle"
                sx={{
                  position: 'absolute',
                  width: '4px',
                  height: '4px',
                  borderRadius: '50%',
                  background: 'rgba(96, 165, 250, 0.3)',
                  animation: `float ${3 + i}s infinite ease-in-out`,
                  animationDelay: `${i * 0.5}s`,
                  left: `${10 + i * 15}%`,
                  top: `${10 + i * 10}%`,
                }}
              />
            ))}

            {/* Connection Lines */}
            {renderConnectionLines()}
            
            {/* Enhanced Location Markers */}
            {filteredLocations.map((location) => (
              <Zoom 
                in={true} 
                timeout={500 + locations.indexOf(location) * 200}
                key={location.id}
              >
                <Box
                  onMouseEnter={() => handleLocationHover(location)}
                  onMouseLeave={handleLocationLeave}
                  onClick={() => handleLocationClick(location)}
                  className={`location-marker ${selectedLocation?.id === location.id ? 'selected' : ''}`}
                  sx={{
                    position: 'absolute',
                    left: `${location.coordinates.x}%`,
                    top: `${location.coordinates.y}%`,
                    transform: 'translate(-50%, -50%)',
                    cursor: 'pointer',
                    zIndex: hoveredLocation?.id === location.id ? 20 : selectedLocation?.id === location.id ? 15 : 10,
                  }}
                >
                  {/* Pulse Animation for Selected Location */}
                  {selectedLocation?.id === location.id && (
                    <Box
                      className="pulse-ring"
                      sx={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        width: '120px',
                        height: '120px',
                        borderRadius: '50%',
                        border: `3px solid ${location.color}`,
                        animation: 'pulseGlow 2s infinite',
                        opacity: 0.7,
                      }}
                    />
                  )}

                  {/* Enhanced Location Icon */}
                  <Box
                    className="location-icon"
                    sx={{
                      width: '80px',
                      height: '80px',
                      borderRadius: '50%',
                      background: `linear-gradient(135deg, ${location.color}40 0%, ${location.color}20 100%)`,
                      border: `3px solid ${location.color}`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: `0 8px 32px ${location.color}40`,
                      backdropFilter: 'blur(10px)',
                      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                      fontSize: '32px',
                      position: 'relative',
                      '&:hover': {
                        transform: 'scale(1.15)',
                        boxShadow: `0 0 32px 8px ${location.color}99`,
                        border: `4px solid ${location.color}`,
                        background: `linear-gradient(135deg, ${location.color}60 0%, ${location.color}40 100%)`,
                        filter: 'drop-shadow(0 0 12px #fff8)'
                      }
                    }}
                  >
                    {location.icon}
                    {/* Glowing border for hover */}
                    {hoveredLocation?.id === location.id && (
                      <Box sx={{
                        position: 'absolute',
                        top: -8,
                        left: -8,
                        width: '96px',
                        height: '96px',
                        borderRadius: '50%',
                        border: `2px solid ${location.color}`,
                        boxShadow: `0 0 24px 4px ${location.color}99`,
                        pointerEvents: 'none',
                        opacity: 0.7,
                        animation: 'pulseGlow 1.5s infinite'
                      }} />
                    )}
                  </Box>

                  {/* Enhanced Location Label */}
                  <Typography
                    variant="caption"
                    className="location-label"
                    sx={{
                      position: 'absolute',
                      top: '90px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      background: `linear-gradient(135deg, ${location.color}90, ${location.color}70)`,
                      padding: '4px 12px',
                      borderRadius: '12px',
                      fontSize: '11px',
                      fontWeight: 'bold',
                      color: 'white',
                      whiteSpace: 'nowrap',
                      backdropFilter: 'blur(10px)',
                      border: `1px solid ${location.color}`,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateX(-50%) scale(1.05)',
                      }
                    }}
                  >
                    {location.name}
                  </Typography>

                  {/* Enhanced Tooltip on Hover */}
                  {hoveredLocation?.id === location.id && (
                    <Grow in={true} timeout={300}>
                      <Card
                        elevation={0}
                        sx={{
                          position: 'absolute',
                          top: '-120px',
                          left: '50%',
                          transform: 'translateX(-50%)',
                          width: '280px',
                          background: 'rgba(13, 27, 42, 0.95)',
                          backdropFilter: 'blur(20px)',
                          border: `1px solid ${location.color}`,
                          borderRadius: '12px',
                          p: 2,
                          zIndex: 30,
                        }}
                      >
                        <Typography variant="h6" sx={{ color: location.color, mb: 1 }}>
                          {location.name}
                        </Typography>
                        <Box display="flex" flexWrap="wrap" gap={0.5} mb={1}>
                          <Chip 
                            label={`Comfort: ${location.seatingComfort}`} 
                            size="small" 
                            variant="outlined"
                            sx={{ fontSize: '10px', color: '#e6f0ff', borderColor: 'rgba(96, 165, 250, 0.3)' }}
                          />
                          <Chip 
                            label={`Noise: ${location.noiseLevel}`} 
                            size="small" 
                            variant="outlined"
                            sx={{ fontSize: '10px', color: '#e6f0ff', borderColor: 'rgba(96, 165, 250, 0.3)' }}
                          />
                        </Box>
                        <Typography variant="body2" sx={{ color: '#e6f0ff', mb: 1 }}>
                          Mood: {location.moodSuitability}
                        </Typography>
                        <Chip 
                          label={`Current: ${location.currentCrowd} crowd`}
                          size="small"
                          sx={{ 
                            backgroundColor: location.currentCrowd === 'Low' ? 'rgba(76, 175, 80, 0.2)' : 
                                           location.currentCrowd === 'Medium' ? 'rgba(255, 152, 0, 0.2)' : 
                                           'rgba(244, 67, 54, 0.2)',
                            color: '#e6f0ff',
                            fontSize: '10px'
                          }}
                        />
                      </Card>
                    </Grow>
                  )}
                </Box>
              </Zoom>
            ))}
          </Box>

          {/* Enhanced Filter Bar at Bottom */}
          <Box 
            className="filter-bar"
            sx={{ 
              position: 'fixed',
              left: '50%',
              bottom: 32,
              transform: 'translateX(-50%)',
              display: 'flex',
              gap: 2,
              justifyContent: 'center',
              p: 1.5,
              background: 'rgba(13, 27, 42, 0.85)',
              borderRadius: '32px',
              boxShadow: '0 2px 12px rgba(96, 165, 250, 0.18)',
              zIndex: 1200
            }}
          >
            {filterOptions.map((filter) => (
              <Button
                key={filter.id}
                variant={activeFilter === filter.id ? "contained" : "outlined"}
                startIcon={filter.icon}
                onClick={() => setActiveFilter(filter.id)}
                className="filter-button"
                sx={{
                  borderRadius: '32px',
                  px: 3,
                  py: 1.5,
                  fontWeight: 600,
                  fontSize: '1rem',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  ...(activeFilter === filter.id ? {
                    background: 'linear-gradient(135deg, #60a5fa, #a78bfa)',
                    color: 'white',
                    boxShadow: '0 4px 16px #60a5fa33',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
                      transform: 'scale(1.08)',
                      boxShadow: '0 8px 25px rgba(96, 165, 250, 0.4)'
                    }
                  } : {
                    color: '#e6f0ff',
                    borderColor: 'rgba(96, 165, 250, 0.3)',
                    '&:hover': {
                      borderColor: '#60a5fa',
                      background: 'rgba(96, 165, 250, 0.1)',
                      transform: 'scale(1.05)'
                    }
                  })
                }}
              >
                {filter.label}
              </Button>
            ))}
          </Box>
        </CardContent>
      </Card>

      {/* Floating Action Buttons */}
      <Box
        className="fab-container"
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          zIndex: 1000
        }}
      >
        <Fab
          color="primary"
          onClick={handleRefreshMap}
          className="refresh-fab"
          sx={{
            background: 'linear-gradient(135deg, #60a5fa, #3b82f6)',
            '&:hover': {
              background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
              transform: 'scale(1.1)',
            }
          }}
        >
          <Refresh />
        </Fab>
        <Fab
          color="secondary"
          onClick={handleSuggestSpot}
          className="suggest-fab"
          sx={{
            background: 'linear-gradient(135deg, #a78bfa, #8b5cf6)',
            '&:hover': {
              background: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
              transform: 'scale(1.1)',
            }
          }}
        >
          <Lightbulb />
        </Fab>
      </Box>

      {/* Enhanced Location Details Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            background: 'rgba(13, 27, 42, 0.95)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(102, 153, 204, 0.18)',
            borderRadius: '20px',
            color: '#e6f0ff'
          }
        }}
      >
        {selectedLocation && (
          <>
            <DialogTitle 
              sx={{ 
                background: `linear-gradient(135deg, ${selectedLocation.color}, ${selectedLocation.color}80)`,
                color: 'white',
                borderRadius: '20px 20px 0 0'
              }}
            >
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box display="flex" alignItems="center">
                  <Box sx={{ fontSize: '40px', mr: 2 }}>
                    {selectedLocation.icon}
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {selectedLocation.name}
                  </Typography>
                </Box>
                <IconButton onClick={handleCloseDialog} sx={{ color: 'white' }}>
                  <Close />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent sx={{ pt: 3 }}>
              <Typography variant="body1" paragraph sx={{ color: '#e6f0ff', fontSize: '16px' }}>
                {selectedLocation.description}
              </Typography>
              
              {/* Enhanced Info Cards */}
              <Grid container spacing={2} mb={3}>
                <Grid item xs={6} md={3}>
                  <Card elevation={0} sx={{ background: 'rgba(96, 165, 250, 0.1)', p: 1.5, borderRadius: '12px' }}>
                    <Typography variant="caption" sx={{ color: '#60a5fa', fontWeight: 'bold' }}>
                      Seating Comfort
                    </Typography>
                    <Typography variant="h6" sx={{ color: '#e6f0ff' }}>
                      {selectedLocation.seatingComfort}
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Card elevation={0} sx={{ background: 'rgba(167, 139, 250, 0.1)', p: 1.5, borderRadius: '12px' }}>
                    <Typography variant="caption" sx={{ color: '#a78bfa', fontWeight: 'bold' }}>
                      Noise Level
                    </Typography>
                    <Typography variant="h6" sx={{ color: '#e6f0ff' }}>
                      {selectedLocation.noiseLevel}
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Card elevation={0} sx={{ background: 'rgba(34, 197, 94, 0.1)', p: 1.5, borderRadius: '12px' }}>
                    <Typography variant="caption" sx={{ color: '#22c55e', fontWeight: 'bold' }}>
                      Current Crowd
                    </Typography>
                    <Typography variant="h6" sx={{ color: '#e6f0ff' }}>
                      {selectedLocation.currentCrowd}
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Card elevation={0} sx={{ background: 'rgba(249, 115, 22, 0.1)', p: 1.5, borderRadius: '12px' }}>
                    <Typography variant="caption" sx={{ color: '#f97316', fontWeight: 'bold' }}>
                      Environment
                    </Typography>
                    <Typography variant="h6" sx={{ color: '#e6f0ff' }}>
                      {selectedLocation.indoor ? 'Indoor' : 'Outdoor'}
                    </Typography>
                  </Card>
                </Grid>
              </Grid>

              <Typography variant="h6" gutterBottom sx={{ color: '#60a5fa', mt: 3 }}>
                Mood Suitability
              </Typography>
              <Typography variant="body1" sx={{ color: '#e6f0ff', mb: 3, fontStyle: 'italic' }}>
                {selectedLocation.moodSuitability}
              </Typography>

              <Typography variant="h6" gutterBottom sx={{ color: '#60a5fa' }}>
                Available Activities
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={1} mb={3}>
                {selectedLocation.activities.map((activity) => (
                  <Chip 
                    key={activity} 
                    label={activity} 
                    size="small"
                    sx={{
                      backgroundColor: `${selectedLocation.color}20`,
                      color: '#e6f0ff',
                      border: `1px solid ${selectedLocation.color}40`
                    }}
                  />
                ))}
              </Box>

              <Typography variant="h6" gutterBottom sx={{ color: '#60a5fa' }}>
                Facilities
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={1} mb={3}>
                {selectedLocation.details.facilities.map((facility) => (
                  <Chip 
                    key={facility} 
                    label={facility} 
                    size="small" 
                    variant="outlined"
                    sx={{
                      color: '#e6f0ff',
                      borderColor: 'rgba(96, 165, 250, 0.3)'
                    }}
                  />
                ))}
              </Box>

              <Typography variant="h6" gutterBottom sx={{ color: '#60a5fa' }}>
                Pro Tip
              </Typography>
              <Typography variant="body2" sx={{ color: '#e6f0ff', fontStyle: 'italic', fontSize: '14px' }}>
                {selectedLocation.details.tips}
              </Typography>
            </DialogContent>
            <DialogActions sx={{ p: 3, gap: 2 }}>
              <Button 
                onClick={handleCloseDialog}
                variant="outlined"
                sx={{
                  color: '#e6f0ff',
                  borderColor: 'rgba(96, 165, 250, 0.3)',
                  '&:hover': {
                    borderColor: '#60a5fa',
                    background: 'rgba(96, 165, 250, 0.1)'
                  }
                }}
              >
                Close
              </Button>
              <Button 
                variant="contained" 
                startIcon={<Directions />}
                onClick={handleCloseDialog}
                sx={{
                  background: `linear-gradient(135deg, ${selectedLocation.color}, ${selectedLocation.color}80)`,
                  '&:hover': {
                    background: `linear-gradient(135deg, ${selectedLocation.color}CC, ${selectedLocation.color}AA)`,
                  }
                }}
              >
                Get Directions
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default InteractiveMap;
